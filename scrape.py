#!/usr/bin/env python3

import os
import re
import time
import json
import requests
import subprocess
import shutil
import argparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc
from dotenv import load_dotenv
import yaml

# Load environment variables
load_dotenv()

def load_cookies_from_file(driver, cookie_file):
    """Load cookies from JSON file"""
    try:
        with open(cookie_file, 'r') as f:
            cookies = json.load(f)
        
        loaded_count = 0
        for cookie in cookies:
            try:
                driver.add_cookie(cookie)
                loaded_count += 1
            except Exception as e:
                print(f"Skipping problematic cookie: {e}")
        
        print(f"Loaded {loaded_count} cookies successfully")
        return True
    except FileNotFoundError:
        print(f"Cookie file {cookie_file} not found")
        return False
    except Exception as e:
        print(f"Error loading cookies: {e}")
        return False

def check_login_status(driver):
    """Check if user is already logged in"""
    try:
        # Look for login indicators
        login_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '/login') or contains(text(), 'Login') or contains(text(), 'Sign In')]")
        if login_elements:
            print("User appears to be logged out")
            return False
        
        # Look for user profile or logout indicators
        profile_elements = driver.find_elements(By.XPATH, "//a[contains(@href, '/profile') or contains(@href, '/account') or contains(text(), 'Logout') or contains(text(), 'Profile')]")
        if profile_elements:
            print("User appears to be logged in")
            return True
        
        print("Login status unclear, assuming logged out")
        return False
    except Exception as e:
        print(f"Error checking login status: {e}")
        return False

def extract_movie_id(url):
    """Extract movie ID from URL"""
    match = re.search(r'/movies/(\d+)', url)
    return match.group(1) if match else None

def clean_text(text):
    """Clean and normalize text"""
    if not text:
        return ""
    return text.strip().replace('\n', ' ').replace('\r', '')

def extract_people(driver):
    """Extract people/actors from the page"""
    try:
        people_container = driver.find_element(By.CLASS_NAME, "movie-bg-player-model-container")
        people_links = people_container.find_elements(By.TAG_NAME, "a")
        
        people = []
        for link in people_links:
            name_span = link.find_element(By.TAG_NAME, "span")
            name = clean_text(name_span.text).rstrip(', ')
            if name:
                people.append(name)
        
        return people
    except Exception as e:
        print(f"Error extracting people: {e}")
        return []

def extract_title(driver):
    """Extract title from h1 element"""
    try:
        title_element = driver.find_element(By.XPATH, "//div[@class='ant-space-item']//h1")
        return clean_text(title_element.text)
    except Exception as e:
        print(f"Error extracting title: {e}")
        return ""

def extract_description(driver):
    """Extract description by clicking the more button and getting the text"""
    try:
        # Click the more button
        more_button = driver.find_element(By.XPATH, "//button[@data-tooltip='More']")
        driver.execute_script("arguments[0].click();", more_button)
        
        # Wait a moment for the description to appear
        time.sleep(1)
        
        # Extract description using the provided CSS selector
        description_element = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#root > div > section > main > section > section > main > div > div > div.movie-player-top-desktop > div.movie-bg-player-info-bar-container > div > div:nth-child(3) > div > div > div > div > div:nth-child(2) > div > div > p"))
        )
        
        return clean_text(description_element.text)
    except Exception as e:
        print(f"Error extracting description: {e}")
        return ""

def extract_tags(driver):
    """Extract tags from the page"""
    try:
        # Extract tags using the provided CSS selector
        tags_container = driver.find_element(By.CSS_SELECTOR, "#root > div > section > main > section > section > main > div > div > div.movie-player-top-desktop > div.movie-bg-player-info-bar-container > div > div:nth-child(3) > div > div > div > div > div:nth-child(4) > div > div")
        
        tag_elements = tags_container.find_elements(By.CLASS_NAME, "ant-tag")
        
        tags = []
        for tag_element in tag_elements:
            tag_text = clean_text(tag_element.text)
            if tag_text:
                tags.append(tag_text)
        
        return tags
    except Exception as e:
        print(f"Error extracting tags: {e}")
        return []

def download_video(driver, movie_id, debug=False):
    """Download video by clicking watch button and extracting highest resolution m3u8 URL"""
    try:
        # Click the Watch button
        print("Clicking Watch button...")
        watch_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button.watch-button"))
        )
        watch_button.click()
        
        # Wait for the page to update and source element to appear
        print("Waiting for video source element to appear...")
        source_element = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "source[type='application/x-mpegURL']"))
        )
        
        # Extract the m3u8 URL from the source element
        m3u8_url = source_element.get_attribute("src")
        print(f"Found m3u8 URL: {m3u8_url}")
        
        # Fetch the m3u8 file content
        print("Fetching m3u8 file content...")
        response = requests.get(m3u8_url)
        response.raise_for_status()
        m3u8_content = response.text
        
        # Parse the m3u8 content to find the highest resolution stream
        highest_resolution_url = parse_highest_resolution_m3u8(m3u8_content, m3u8_url)
        
        if highest_resolution_url:
            print(f"Highest resolution stream URL: {highest_resolution_url}")
            
            # Download to /tmp/ first for better performance
            temp_file = f"/tmp/{movie_id}.mp4"
            final_file = f"/Volumes/4To/videos/{movie_id}.mp4"
            
            print(f"Downloading video to temporary location: {temp_file}...")
            if debug:
                print(f"Source URL: {highest_resolution_url}")
            
            ffmpeg_command = ["ffmpeg"]
            
            if debug:
                ffmpeg_command.extend(["-v", "info"])  # Verbose logging
                ffmpeg_command.extend(["-stats"])      # Show progress statistics
            else:
                ffmpeg_command.extend(["-v", "quiet"])  # Quiet mode
                ffmpeg_command.extend(["-stats"])       # Still show basic progress
            
            ffmpeg_command.extend([
                "-i", highest_resolution_url,
                "-c", "copy",
                "-y",          # Overwrite output file if it exists
                temp_file
            ])
            print(f"Running ffmpeg...")
            if debug:
                print(f"Running ffmpeg command: {' '.join(ffmpeg_command)}")
            print("Starting download... (this may take a while)")
            
            # Run without capturing output to see real-time progress
            result = subprocess.run(ffmpeg_command)
            
            if result.returncode == 0:
                print(f"\n✅ Video downloaded successfully to temp location: {temp_file}")
                
                # Check if file actually exists and has content
                if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                    file_size = os.path.getsize(temp_file)
                    print(f"Downloaded file size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
                    
                    # Ensure final videos directory exists
                    os.makedirs("/Volumes/4To/videos", exist_ok=True)
                    
                    # Move the file from temp to final location
                    print(f"Moving video from {temp_file} to {final_file}...")
                    shutil.move(temp_file, final_file)
                    print(f"✅ Video moved successfully to: {final_file}")
                else:
                    print(f"❌ Downloaded file is empty or doesn't exist: {temp_file}")
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
            else:
                print(f"❌ FFmpeg failed with return code: {result.returncode}")
                print("Check the output above for error details.")
                # Clean up temp file if download failed
                if os.path.exists(temp_file):
                    print(f"Cleaning up failed download: {temp_file}")
                    os.remove(temp_file)
        else:
            print("Could not find highest resolution stream")
        
    except TimeoutException:
        print("Timeout waiting for video source element")
    except requests.RequestException as e:
        print(f"Error fetching m3u8 file: {e}")
    except subprocess.CalledProcessError as e:
        print(f"Error running ffmpeg: {e}")
    except Exception as e:
        print(f"Error during video download: {e}")

def parse_highest_resolution_m3u8(m3u8_content, base_url):
    """Parse m3u8 content and return the URL of the highest resolution stream"""
    lines = m3u8_content.strip().split('\n')
    highest_resolution = 0
    highest_resolution_file = None
    
    for i, line in enumerate(lines):
        if line.startswith('#EXT-X-STREAM-INF:'):
            # Extract resolution from the stream info line
            resolution_match = re.search(r'RESOLUTION=(\d+)x(\d+)', line)
            if resolution_match:
                width = int(resolution_match.group(1))
                height = int(resolution_match.group(2))
                resolution = width * height
                
                # Check if this is the highest resolution so far
                if resolution > highest_resolution:
                    highest_resolution = resolution
                    # The next line should contain the m3u8 file name
                    if i + 1 < len(lines):
                        m3u8_file = lines[i + 1].strip()
                        if m3u8_file and not m3u8_file.startswith('#'):
                            highest_resolution_file = m3u8_file
    
    if highest_resolution_file:
        # Construct the full URL
        base_url_parts = base_url.rsplit('/', 1)
        if len(base_url_parts) == 2:
            return f"{base_url_parts[0]}/{highest_resolution_file}"
        else:
            return highest_resolution_file
    
    return None

def save_movie_data(movie_id, title, description, tags, people):
    """Save movie data to YAML file"""
    try:
        # Ensure videos directory exists
        os.makedirs("videos", exist_ok=True)
        
        # Prepare data structure in the specified order
        data = {
            'title': title,
            'desc': description,
            'people': people,
            'tags': tags
        }
        
        # Save to YAML file with inline format for people and tags
        filename = f"videos/{movie_id}.yaml"
        with open(filename, 'w', encoding='utf-8') as f:
            # Write title in block format
            f.write(f"title: {yaml.dump(title, default_flow_style=False).strip()}\n")
            # Write description using literal block scalar for long text
            f.write("desc: >\n")
            # Indent each line of the description
            for line in description.split('\n'):
                f.write(f"  {line}\n")
            # Write people and tags in inline format
            f.write(f"people: {yaml.dump(people, default_flow_style=True)}\n")
            f.write(f"tags: {yaml.dump(tags, default_flow_style=True)}\n")
        
        print(f"Saved data to {filename}")
        return True
    except Exception as e:
        print(f"Error saving movie data: {e}")
        return False

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Movie scraping script')
    parser.add_argument('-bug', '--debug', action='store_true', help='Enable debug mode with verbose ffmpeg output')
    args = parser.parse_args()
    
    debug_mode = args.debug
    
    print("Starting movie scraping script...")
    if debug_mode:
        print("Debug mode enabled - verbose ffmpeg output will be shown")
    print("Debug: Script started successfully")
    
    # Read URLs from file
    try:
        print("Debug: Attempting to read urls.txt")
        with open('urls.txt', 'r') as f:
            urls = [line.strip() for line in f if line.strip()]
        print(f"Found {len(urls)} URLs to process")
        print(f"Debug: First URL: {urls[0] if urls else 'No URLs found'}")
    except FileNotFoundError:
        print("urls.txt file not found!")
        return
    except Exception as e:
        print(f"Error reading urls.txt: {e}")
        return
    
    # Setup Chrome driver
    print("Debug: Setting up Chrome driver...")
    try:
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--mute-audio")
        options.add_argument("--disable-audio-output")
        # options.add_argument("--disable-background-media-suspend")
        
        print("Debug: Initializing Chrome driver...")
        driver = uc.Chrome(options=options)
        print("Debug: Chrome driver initialized successfully")
    except Exception as e:
        print(f"Error initializing Chrome driver: {e}")
        print("Please make sure Chrome is installed and accessible.")
        return
    
    try:
        # Navigate to the main site
        print("Navigating to TeamSkeet...")
        driver.get("https://app.teamskeet.com/")
        print("Debug: Navigation completed")
        time.sleep(3)
        
        # Load cookies
        print("Debug: Loading cookies...")
        cookie_file = "teamskeet_cookies.json"
        if load_cookies_from_file(driver, cookie_file):
            print("Debug: Refreshing page after loading cookies...")
            driver.refresh()
            time.sleep(3)
            print("Debug: Page refresh completed")
        
        # Check login status
        login_status = check_login_status(driver)
        if login_status:
            print("User is logged in, proceeding with scraping...")
        else:
            print("Login status unclear, but cookies were loaded. Attempting to proceed...")
            # Try to access a protected page to verify login
            try:
                driver.get("https://app.teamskeet.com/")
                time.sleep(2)
                # If we can access the page without being redirected to login, we're probably logged in
                current_url = driver.current_url
                if "login" not in current_url.lower():
                    print("Successfully accessed protected content, proceeding with scraping...")
                else:
                    print("Redirected to login page. Please run rip.py first to authenticate.")
                    return
            except Exception as e:
                print(f"Error verifying login status: {e}")
                print("Proceeding anyway...")
        
        # Click "Access Your Content" if present (using same method as rip.py)
        try:
            access_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div.access-button"))
            )
            print("✅ 'Access Your Content' button found")
            access_button.click()
            print("✅ 'Access Your Content' button clicked successfully")
            time.sleep(5)  # Wait for page to load after clicking
            
            # Click the close button to dismiss popup and prevent future popups
            try:
                print("Looking for popup close button after accessing content...")
                close_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "#root > div > section > main > div > div > div > header > div.promobar-container > div > div.close-button"))
                )
                close_button.click()
                print("Clicked popup close button after accessing content")
                time.sleep(2)
            except TimeoutException:
                print("No popup close button found after accessing content, continuing...")
                
        except TimeoutException:
            print("⚠️ 'Access Your Content' button not found - may not be required or page layout changed")
        
        # Process each URL
        successful_count = 0
        for i, url in enumerate(urls, 1):
            print(f"\nProcessing URL {i}/{len(urls)}: {url}")
            
            # Extract movie ID
            movie_id = extract_movie_id(url)
            if not movie_id:
                print(f"Could not extract movie ID from URL: {url}")
                continue
            
            
            # Check if video file already exists in destination directory
            video_file = f"/Volumes/4To/videos/{movie_id}.mp4"
            if os.path.exists(video_file):
                print(f"Video {movie_id}.mp4 already exists in destination directory. Skipping entire processing.")
                continue
            
            try:
                # Check for 'Access Your Content' button before processing
                try:
                    access_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "div.access-button"))
                    )
                    print("🔄 'Access Your Content' button appeared again, clicking it...")
                    access_button.click()
                    time.sleep(3)  # Wait for page to load
                    print("✅ 'Access Your Content' button clicked, continuing...")
                except TimeoutException:
                    # Button not found, continue normally
                    pass
                # Navigate to the movie page
                driver.get(url)
                time.sleep(3)
                
                # Extract data
                print(f"Extracting data for movie ID: {movie_id}")
                title = extract_title(driver)
                people = extract_people(driver)
                description = extract_description(driver)
                tags = extract_tags(driver)
                
                print(f"Title: {title}")
                print(f"People: {people}")
                print(f"Description: {description[:100]}..." if len(description) > 100 else f"Description: {description}")
                print(f"Tags: {tags}")
                
                # Download video before saving data
                print(f"Starting video download for movie ID: {movie_id}")
                download_video(driver, movie_id, debug_mode)
                
                # Save data
                if save_movie_data(movie_id, title, description, tags, people):
                    successful_count += 1
                
                # Small delay between requests
                time.sleep(2)
                
            except Exception as e:
                print(f"Error processing URL {url}: {e}")
                continue
        
        print(f"\nScraping completed! Successfully processed {successful_count}/{len(urls)} movies.")
        
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        print("Keeping browser open for inspection...")
        input("Press Enter to close the browser...")
        driver.quit()

if __name__ == "__main__":
    main()