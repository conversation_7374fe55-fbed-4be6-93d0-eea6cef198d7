import cv2
import numpy as np
import os
from typing import Tuple, List, Optional, Dict
import argparse
from sklearn.cluster import DBSCAN
from collections import Counter

class AutoLogoDetector:
    def __init__(self, video_path: str):
        """
        Initialise le détecteur automatique de logo
        
        Args:
            video_path: Chemin vers la vidéo
        """
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        
        if not self.cap.isOpened():
            raise ValueError(f"Impossible d'ouvrir la vidéo: {video_path}")
        
        # Propriétés de la vidéo
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.duration = self.total_frames / self.fps
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
    def get_sample_frames(self, num_samples: int, skip_minutes_start: float = 1.0, 
                         skip_minutes_end: float = 1.0) -> List[int]:
        """
        Génère une liste de numéros de frames à échantillonner
        """
        start_frame = int(skip_minutes_start * 60 * self.fps)
        end_frame = self.total_frames - int(skip_minutes_end * 60 * self.fps)
        
        if start_frame >= end_frame:
            raise ValueError("Période d'échantillonnage invalide: trop de minutes à éviter")
        
        frame_indices = np.linspace(start_frame, end_frame, num_samples, dtype=int)
        return frame_indices.tolist()
    
    def extract_features_from_frame(self, frame: np.ndarray, min_area: int = 100) -> List[Dict]:
        """
        Extrait les contours/features d'une frame qui pourraient être des logos
        
        Args:
            frame: Image de la frame
            min_area: Aire minimale pour considérer un contour
            
        Returns:
            Liste de dictionnaires avec les caractéristiques des contours
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Appliquer un flou gaussien pour réduire le bruit
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Détection de contours avec différents seuils
        features = []
        
        # Paramètres pour filtrer les détections
        max_area = (self.width * self.height) // 10  # Maximum 5% de l'écran
        min_width, min_height = 30, 20  # Taille minimale raisonnable pour un logo
        max_width = self.width // 3  # Maximum 1/3 de la largeur
        max_height = self.height // 3  # Maximum 1/3 de la hauteur
        
        # Méthode 1: Détection de contours par seuillage adaptatif
        thresh_adaptive = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                              cv2.THRESH_BINARY, 11, 2)
        contours, _ = cv2.findContours(thresh_adaptive, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area < area < max_area:
                x, y, w, h = cv2.boundingRect(contour)
                # Filtrer par taille et position
                if (x > 20 and y > 20 and x + w < self.width - 20 and y + h < self.height - 20 and
                    min_width <= w <= max_width and min_height <= h <= max_height):
                    # Calculer le ratio aspect pour éviter les formes trop étirées
                    aspect_ratio = max(w, h) / min(w, h)
                    if aspect_ratio < 5:  # Éviter les lignes trop fines
                        features.append({
                            'x': x, 'y': y, 'width': w, 'height': h,
                            'area': area, 'type': 'contour', 'aspect_ratio': aspect_ratio
                        })
        
        # Méthode 2: Détection de zones de haute variance (texte/logos complexes) - améliorée
        kernel_size = 15
        kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
        mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        sqr_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel)
        variance = sqr_mean - mean**2
        
        # Utiliser un seuil modéré pour la variance
        variance_flat = variance.flatten().astype(np.float64)
        variance_threshold = np.percentile(variance_flat, 80)  # Plus permissif
        high_var_mask = variance > variance_threshold
        
        # Morphologie pour connecter les régions proches
        kernel_morph = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        high_var_mask = cv2.morphologyEx(high_var_mask.astype(np.uint8), cv2.MORPH_CLOSE, kernel_morph)
        
        high_var_contours, _ = cv2.findContours(high_var_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in high_var_contours:
            area = cv2.contourArea(contour)
            if min_area < area < max_area:
                x, y, w, h = cv2.boundingRect(contour)
                if (x > 20 and y > 20 and x + w < self.width - 20 and y + h < self.height - 20 and
                    min_width <= w <= max_width and min_height <= h <= max_height):
                    aspect_ratio = max(w, h) / min(w, h)
                    if aspect_ratio < 4:  # Plus strict pour la variance
                        features.append({
                            'x': x, 'y': y, 'width': w, 'height': h,
                            'area': area, 'type': 'high_variance', 'aspect_ratio': aspect_ratio
                        })
        
        # Fusionner les régions qui se chevauchent significativement
        features = self._merge_overlapping_features(features)
        
        return features
    
    def _merge_overlapping_features(self, features: List[Dict]) -> List[Dict]:
        """
        Fusionne les features qui se chevauchent pour éviter les détections fragmentées
        """
        if len(features) <= 1:
            return features
        
        merged = []
        used = set()
        
        for i, feat1 in enumerate(features):
            if i in used:
                continue
                
            # Commencer avec la feature actuelle
            merged_feat = feat1.copy()
            group = [feat1]
            used.add(i)
            
            # Chercher les features qui se chevauchent
            for j, feat2 in enumerate(features[i+1:], i+1):
                if j in used:
                    continue
                    
                # Calculer le chevauchement
                overlap_ratio = self._calculate_overlap_ratio(feat1, feat2)
                
                # Si chevauchement significatif, fusionner
                if overlap_ratio > 0.3:  # 30% de chevauchement
                    group.append(feat2)
                    used.add(j)
            
            # Si on a trouvé des features à fusionner
            if len(group) > 1:
                # Calculer la boîte englobante
                min_x = min(f['x'] for f in group)
                min_y = min(f['y'] for f in group)
                max_x = max(f['x'] + f['width'] for f in group)
                max_y = max(f['y'] + f['height'] for f in group)
                
                merged_feat = {
                    'x': min_x,
                    'y': min_y,
                    'width': max_x - min_x,
                    'height': max_y - min_y,
                    'area': (max_x - min_x) * (max_y - min_y),
                    'type': 'merged',
                    'aspect_ratio': max(max_x - min_x, max_y - min_y) / min(max_x - min_x, max_y - min_y)
                }
            
            merged.append(merged_feat)
        
        return merged
    
    def _calculate_overlap_ratio(self, feat1: Dict, feat2: Dict) -> float:
        """
        Calcule le ratio de chevauchement entre deux features
        """
        x1, y1, w1, h1 = feat1['x'], feat1['y'], feat1['width'], feat1['height']
        x2, y2, w2, h2 = feat2['x'], feat2['y'], feat2['width'], feat2['height']
        
        # Calculer l'intersection
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)
        
        if left < right and top < bottom:
            intersection = (right - left) * (bottom - top)
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            return intersection / union if union > 0 else 0
        
        return 0
    
    def cluster_features(self, all_features: List[List[Dict]], eps: float = 25, 
                         min_samples: int = 3) -> List[Dict]:
        """
        Groupe les features similaires détectées dans plusieurs frames
        
        Args:
            all_features: Liste des features pour chaque frame
            eps: Distance maximale entre les points pour être dans le même cluster
            min_samples: Nombre minimum d'échantillons pour former un cluster
            
        Returns:
            Liste des logos potentiels avec leurs caractéristiques
        """
        if not all_features:
            return []
        
        # Collecter toutes les positions centrales des features
        positions = []
        feature_data = []
        
        for frame_idx, features in enumerate(all_features):
            for feature in features:
                center_x = feature['x'] + feature['width'] // 2
                center_y = feature['y'] + feature['height'] // 2
                positions.append([center_x, center_y])
                feature_data.append({**feature, 'frame_idx': frame_idx, 
                                   'center_x': center_x, 'center_y': center_y})
        
        if len(positions) < min_samples:
            return []
        
        # Clustering DBSCAN pour grouper les positions similaires
        positions_array = np.array(positions)
        clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(positions_array)
        
        # Analyser les clusters
        clusters = {}
        for idx, label in enumerate(clustering.labels_):
            if label != -1:  # Ignorer le bruit
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append(feature_data[idx])
        
        # Analyser chaque cluster pour déterminer les logos potentiels
        potential_logos = []
        
        for cluster_id, cluster_features in clusters.items():
            if len(cluster_features) < min_samples:
                continue
            
            # Calculer les statistiques du cluster
            x_coords = [f['x'] for f in cluster_features]
            y_coords = [f['y'] for f in cluster_features]
            widths = [f['width'] for f in cluster_features]
            heights = [f['height'] for f in cluster_features]
            areas = [f['area'] for f in cluster_features]
            
            # Position moyenne et stabilité
            avg_x = np.mean(x_coords)
            avg_y = np.mean(y_coords)
            std_x = np.std(x_coords)
            std_y = np.std(y_coords)
            
            # Dimensions moyennes
            avg_width = np.mean(widths)
            avg_height = np.mean(heights)
            avg_area = np.mean(areas)
            
            # Calculer la fréquence d'apparition
            frame_indices = set(f['frame_idx'] for f in cluster_features)
            appearance_rate = len(frame_indices) / len(all_features)
            
            # Critères de qualité équilibrés avec debug
            # 1. Stabilité de position (modéré)
            stability_score = 1.0 / (1.0 + std_x + std_y)
            is_stable = std_x < 15 and std_y < 15  # Plus permissif
            
            # 2. Fréquence d'apparition (modérée)
            frequency_score = appearance_rate
            is_frequent = appearance_rate >= 0.4  # Plus permissif
            
            # 3. Taille raisonnable (plus permissive)
            min_logo_area = 300  # Plus bas pour capturer plus de logos
            max_logo_area = (self.width * self.height) // 15  # Maximum 6.7% de l'écran
            size_score = 1.0 if min_logo_area <= float(avg_area) <= max_logo_area else 0.1
            is_good_size = min_logo_area <= float(avg_area) <= max_logo_area
            
            # 4. Ratio d'aspect raisonnable (plus permissif)
            avg_aspect_ratio = avg_width / avg_height if avg_height > 0 else 10
            aspect_score = 1.0 if 0.1 <= avg_aspect_ratio <= 8.0 else 0.2
            is_good_aspect = 0.1 <= avg_aspect_ratio <= 8.0
            
            # 5. Consistance des dimensions (plus permissive)
            width_std = np.std(widths)
            height_std = np.std(heights)
            dimension_consistency = 1.0 / (1.0 + width_std/avg_width + height_std/avg_height)
            is_consistent = (width_std/avg_width < 0.5) and (height_std/avg_height < 0.5)  # Plus permissif
            
            # Debug: afficher les statistiques de chaque cluster
            print(f"\n--- Cluster {len(potential_logos)+1} ---")
            print(f"Position: ({avg_x:.1f}, {avg_y:.1f}), Taille: {avg_area:.0f}")
            print(f"Stabilité: std_x={std_x:.1f}, std_y={std_y:.1f} -> {is_stable}")
            print(f"Fréquence: {appearance_rate:.2f} -> {is_frequent}")
            print(f"Taille: {avg_area:.0f} [{min_logo_area}-{max_logo_area}] -> {is_good_size}")
            print(f"Aspect: {avg_aspect_ratio:.2f} -> {is_good_aspect}")
            print(f"Consistance: w_std={width_std/avg_width:.2f}, h_std={height_std/avg_height:.2f} -> {is_consistent}")
            
            # Score total avec pondération
            total_score = (
                stability_score * 0.25 +
                frequency_score * 0.3 +
                size_score * 0.2 +
                aspect_score * 0.15 +
                dimension_consistency * 0.1
            )
            
            # Filtrer les logos de mauvaise qualité
            quality_checks = [
                is_stable,
                is_frequent,
                is_good_size,
                is_good_aspect,
                is_consistent
            ]
            
            # Doit passer au moins 2 des 5 critères (plus permissif)
            passed_checks = sum(quality_checks)
            print(f"Critères passés: {passed_checks}/5")
            if passed_checks >= 2:
                potential_logos.append({
                    'position': {
                        'x': int(avg_x),
                        'y': int(avg_y),
                        'center_x': int(avg_x + avg_width // 2),
                        'center_y': int(avg_y + avg_height // 2)
                    },
                    'dimensions': {
                        'width': int(avg_width),
                        'height': int(avg_height),
                        'area': int(avg_area)
                    },
                    'stability': {
                        'std_x': float(std_x),
                        'std_y': float(std_y),
                        'is_stable': is_stable
                    },
                    'frequency': {
                        'appearance_rate': float(appearance_rate),
                        'detections': len(cluster_features),
                        'frames_detected': len(frame_indices)
                    },
                    'quality_score': float(total_score),
                    'quality_checks': {
                        'stable': is_stable,
                        'frequent': is_frequent,
                        'good_size': is_good_size,
                        'good_aspect': is_good_aspect,
                        'consistent': is_consistent,
                        'passed': sum(quality_checks)
                    },
                    'cluster_id': cluster_id
                })
        
        # Éliminer les logos qui se chevauchent (garder le meilleur)
        potential_logos = self._remove_overlapping_logos(potential_logos)
        
        # Trier par score de qualité
        potential_logos.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return potential_logos
    
    def _remove_overlapping_logos(self, logos: List[Dict]) -> List[Dict]:
        """
        Supprime les logos qui se chevauchent en gardant celui avec le meilleur score
        """
        if len(logos) <= 1:
            return logos
        
        # Trier par score décroissant
        sorted_logos = sorted(logos, key=lambda x: x['quality_score'], reverse=True)
        filtered = []
        
        for logo in sorted_logos:
            # Vérifier s'il chevauche avec un logo déjà accepté
            overlaps = False
            for accepted in filtered:
                overlap_ratio = self._calculate_logo_overlap(logo, accepted)
                if overlap_ratio > 0.5:  # 50% de chevauchement
                    overlaps = True
                    break
            
            if not overlaps:
                filtered.append(logo)
        
        return filtered
    
    def _calculate_logo_overlap(self, logo1: Dict, logo2: Dict) -> float:
        """
        Calcule le ratio de chevauchement entre deux logos
        """
        pos1, dim1 = logo1['position'], logo1['dimensions']
        pos2, dim2 = logo2['position'], logo2['dimensions']
        
        x1, y1, w1, h1 = pos1['x'], pos1['y'], dim1['width'], dim1['height']
        x2, y2, w2, h2 = pos2['x'], pos2['y'], dim2['width'], dim2['height']
        
        # Calculer l'intersection
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)
        
        if left < right and top < bottom:
            intersection = (right - left) * (bottom - top)
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            return intersection / union if union > 0 else 0
        
        return 0
    
    def process_video(self, num_samples: int = 15, skip_minutes_start: float = 1.0,
                     skip_minutes_end: float = 1.0, min_logo_area: int = 200,
                     save_detections: bool = False, output_dir: str = "auto_detections") -> Dict:
        """
        Traite la vidéo pour détecter automatiquement les logos
        
        Args:
            num_samples: Nombre d'images à échantillonner
            skip_minutes_start: Minutes à éviter au début
            skip_minutes_end: Minutes à éviter à la fin
            min_logo_area: Aire minimale pour considérer un élément
            save_detections: Sauvegarder les images avec détections
            output_dir: Dossier de sortie
            
        Returns:
            Dictionnaire avec les résultats
        """
        print(f"Durée de la vidéo: {self.duration:.2f} secondes")
        print(f"Total frames: {self.total_frames}")
        print(f"Résolution: {self.width}x{self.height}")
        print(f"FPS: {self.fps}")
        
        # Obtenir les frames à échantillonner
        sample_frames = self.get_sample_frames(num_samples, skip_minutes_start, skip_minutes_end)
        
        all_features = []
        sample_images = []
        
        if save_detections and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"\nAnalyse de {num_samples} frames pour détecter les logos...")
        
        # Extraire les features de chaque frame
        for i, frame_num in enumerate(sample_frames):
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = self.cap.read()
            
            if not ret:
                print(f"Erreur lors de la lecture de la frame {frame_num}")
                continue
            
            print(f"Traitement frame {frame_num} ({i+1}/{num_samples})...")
            
            features = self.extract_features_from_frame(frame, min_logo_area)
            all_features.append(features)
            sample_images.append((frame_num, frame.copy()))
        
        print("\nRegroupement des éléments détectés...")
        
        # Grouper les features similaires
        potential_logos = self.cluster_features(all_features, eps=30, min_samples=max(3, num_samples//5))
        
        # Sauvegarder les résultats si demandé
        if save_detections and potential_logos:
            self.save_detection_images(sample_images, potential_logos, output_dir)
        
        # Préparer les résultats
        results = {
            'logos_found': len(potential_logos) > 0,
            'num_potential_logos': len(potential_logos),
            'video_path': self.video_path,  # Ajout du chemin de la vidéo
            'video_info': {
                'duration': self.duration,
                'resolution': f"{self.width}x{self.height}",
                'fps': self.fps
            },
            'analysis_params': {
                'samples_analyzed': len(all_features),
                'skip_start_minutes': skip_minutes_start,
                'skip_end_minutes': skip_minutes_end
            },
            'potential_logos': potential_logos[:5]  # Limiter aux 5 meilleurs
        }
        
        return results
    
    def save_detection_images(self, sample_images: List[Tuple[int, np.ndarray]], 
                            potential_logos: List[Dict], output_dir: str):
        """Sauvegarde les images avec les détections marquées"""
        
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]
        
        for frame_num, frame in sample_images:
            frame_copy = frame.copy()
            
            # Dessiner les logos potentiels
            for i, logo in enumerate(potential_logos[:5]):
                if i < len(colors):
                    color = colors[i]
                    pos = logo['position']
                    dims = logo['dimensions']
                    
                    # Rectangle
                    cv2.rectangle(frame_copy, (pos['x'], pos['y']), 
                                (pos['x'] + dims['width'], pos['y'] + dims['height']), 
                                color, 2)
                    
                    # Étiquette
                    label = f"Logo {i+1} (Score: {logo['quality_score']:.2f})"
                    cv2.putText(frame_copy, label, (pos['x'], pos['y']-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Sauvegarder
            output_path = os.path.join(output_dir, f"detection_frame_{frame_num}.jpg")
            cv2.imwrite(output_path, frame_copy)
    
    def print_results(self, results: Dict):
        """Affiche les résultats de façon formatée"""
        print("\n" + "="*60)
        print("RÉSULTATS DE DÉTECTION AUTOMATIQUE DE LOGO")
        print("="*60)
        
        if not results['logos_found']:
            print("❌ Aucun logo fixe détecté de manière consistante")
            print(f"   Frames analysées: {results['analysis_params']['samples_analyzed']}")
            print("\n💡 CONSEILS POUR AMÉLIORER LA DÉTECTION:")
            print("   • Augmentez le nombre d'échantillons (-n)")
            print("   • Réduisez l'aire minimale (-a)")
            print("   • Vérifiez que le logo est visible et stable")
            return
        
        print(f"✅ {results['num_potential_logos']} logo(s) de qualité détecté(s)")
        print(f"Frames analysées: {results['analysis_params']['samples_analyzed']}")
        
        for i, logo in enumerate(results['potential_logos'], 1):
            print(f"\n📍 LOGO {i}:")
            print(f"   Position: ({logo['position']['x']}, {logo['position']['y']})")
            print(f"   Dimensions: {logo['dimensions']['width']} x {logo['dimensions']['height']} px")
            print(f"   Surface: {logo['dimensions']['area']} px²")
            
            # Affichage détaillé des critères de qualité
            if 'quality_checks' in logo:
                checks = logo['quality_checks']
                print(f"   Critères de qualité ({checks['passed']}/5):")
                print(f"     {'✓' if checks['stable'] else '✗'} Stabilité: {'Stable' if checks['stable'] else 'Variable'}")
                print(f"       Écart-type: X={logo['stability']['std_x']:.1f}, Y={logo['stability']['std_y']:.1f}")
                print(f"     {'✓' if checks['frequent'] else '✗'} Fréquence: {logo['frequency']['appearance_rate']:.1%} des frames")
                print(f"     {'✓' if checks['good_size'] else '✗'} Taille: {'Appropriée' if checks['good_size'] else 'Inappropriée'}")
                print(f"     {'✓' if checks['good_aspect'] else '✗'} Proportions: {'Bonnes' if checks['good_aspect'] else 'Étirées'}")
                print(f"     {'✓' if checks['consistent'] else '✗'} Consistance: {'Stable' if checks['consistent'] else 'Variable'}")
            else:
                # Compatibilité avec l'ancien format
                print(f"   Stabilité: {'✓ Stable' if logo['stability']['is_stable'] else '⚠ Variable'}")
                print(f"     Écart-type: X={logo['stability']['std_x']:.1f}, Y={logo['stability']['std_y']:.1f}")
                print(f"   Fréquence: {logo['frequency']['appearance_rate']:.1%} des frames")
            
            print(f"   Score qualité: {logo['quality_score']:.3f}")
        
        # Conseils d'utilisation
        print(f"\n💡 UTILISATION:")
        best_logo = results['potential_logos'][0]
        pos = best_logo['position']
        dims = best_logo['dimensions']
        
        print(f"   📺 Pour prévisualiser le cadre du logo:")
        print(f"   ffplay -i {results['video_path']} -vf \"drawbox=x={pos['x']}:y={pos['y']}:w={dims['width']}:h={dims['height']}:color=red:thickness=3\"")
        
        print(f"\n   🎬 Pour masquer le logo le mieux détecté:")
        print(f"   ffmpeg -i {results['video_path']} -vf \"delogo=x={pos['x']}:y={pos['y']}:w={dims['width']}:h={dims['height']}\" output.mp4")
    
    def __del__(self):
        """Libère les ressources"""
        if hasattr(self, 'cap'):
            self.cap.release()

def main():
    parser = argparse.ArgumentParser(description='Détecteur automatique de logo dans vidéo')
    parser.add_argument('video', help='Chemin vers la vidéo')
    parser.add_argument('-n', '--samples', type=int, default=7, 
                       help='Nombre d\'images à échantillonner (défaut: 7)')
    parser.add_argument('-s', '--skip-start', type=float, default=1.0,
                       help='Minutes à éviter au début (défaut: 1.0)')
    parser.add_argument('-e', '--skip-end', type=float, default=1.0,
                       help='Minutes à éviter à la fin (défaut: 1.0)')
    parser.add_argument('-a', '--min-area', type=int, default=20,
                       help='Aire minimale pour un logo (défaut: 20)')
    parser.add_argument('--save', action='store_true',
                       help='Sauvegarder les images avec détections')
    parser.add_argument('-o', '--output', default='auto_detections',
                       help='Dossier de sortie (défaut: auto_detections)')
    
    args = parser.parse_args()
    
    try:
        # Créer le détecteur
        detector = AutoLogoDetector(args.video)
        
        # Traiter la vidéo
        results = detector.process_video(
            num_samples=args.samples,
            skip_minutes_start=args.skip_start,
            skip_minutes_end=args.skip_end,
            min_logo_area=args.min_area,
            save_detections=args.save,
            output_dir=args.output
        )
        
        # Afficher les résultats
        detector.print_results(results)
        
    except Exception as e:
        print(f"Erreur: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())