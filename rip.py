import os
import time
import json
import signal
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

COOKIES_FILE = 'teamskeet_cookies.json'

def verbose_log(message):
    """Print verbose log messages"""
    print(f"[VERBOSE] {message}")

def save_cookies(driver):
    """Save cookies to file"""
    try:
        # Check if the driver session is still active
        if driver.session_id is None:
            verbose_log("⚠️ WebDriver session is already closed, cannot save cookies")
            return
            
        cookies = driver.get_cookies()
        with open(COOKIES_FILE, 'w') as f:
            json.dump(cookies, f, indent=2)
        verbose_log("✅ Cookies saved successfully")
    except Exception as error:
        verbose_log(f"❌ Failed to save cookies: {error}")

def navigate_and_save_cookies(driver, url):
    """Navigate to URL and save cookies after navigation"""
    verbose_log(f"Navigating to {url}")
    driver.get(url)
    
    # Wait for page to load
    WebDriverWait(driver, 30).until(
        lambda d: d.execute_script("return document.readyState") == "complete"
    )
    verbose_log(f"✅ Successfully navigated to {url}")
    
    # Save cookies after navigation
    save_cookies(driver)

def load_cookies(driver):
    """Load cookies from file"""
    try:
        if os.path.exists(COOKIES_FILE):
            with open(COOKIES_FILE, 'r') as f:
                cookies = json.load(f)
            
            current_domain = driver.current_url
            loaded_count = 0
            
            for cookie in cookies:
                try:
                    # Filter cookies to match current domain
                    cookie_domain = cookie.get('domain', '')
                    if 'teamskeet.com' in current_domain and ('teamskeet.com' in cookie_domain or cookie_domain.startswith('.teamskeet.com')):
                        # Remove problematic fields that can cause domain mismatch
                        clean_cookie = {k: v for k, v in cookie.items() if k not in ['sameSite', 'expiry']}
                        driver.add_cookie(clean_cookie)
                        loaded_count += 1
                except Exception as cookie_error:
                    verbose_log(f"⚠️ Skipped problematic cookie: {cookie_error}")
                    continue
            
            if loaded_count > 0:
                verbose_log(f"✅ Cookies loaded successfully ({loaded_count} cookies)")
                return True
            else:
                verbose_log("⚠️ No compatible cookies found for current domain")
    except Exception as error:
        verbose_log(f"❌ Failed to load cookies: {error}")
    return False

def is_logged_in(driver):
    """Check if user is already logged in"""
    try:
        verbose_log("Checking if user is already logged in...")
        
        # Navigate to the movies page to check login status
        driver.get("https://app.teamskeet.com/")
        
        # Wait for page to load
        WebDriverWait(driver, 30).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # Wait a moment for the page to fully load
        time.sleep(7)
        
        # Check if login button is present
        try:
            login_elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Login')] | //input[@type='submit' and contains(@value, 'Login')] | //a[contains(@href, 'login')]")
            if not login_elements:
                verbose_log("✅ User is already logged in - no login button found")
                return True
            else:
                verbose_log("❌ User is not logged in - login button found")
                return False
        except NoSuchElementException:
            verbose_log("✅ User is already logged in - no login button found")
            return True
            
    except Exception as error:
        verbose_log(f"❌ Error checking login status: {error}")
        return False

def main():
    driver = None
    email = None
    password = None
    
    def signal_handler(signum, frame):
        """Handle termination signals"""
        verbose_log(f"Received signal {signum}. Final cookie save and closing browser...")
        if driver:
            save_cookies(driver)
        exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal
    
    try:
        verbose_log("Starting undetected Chrome browser...")
        
        # Get credentials from environment first
        email = os.getenv('freeuse_user')
        password = os.getenv('freeuse_password')
        
        if not email or not password:
            raise ValueError("Missing credentials in .env file. Please set freeuse_user and freeuse_password")
        
        # Configure Chrome options for stealth
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins-discovery")
        
        # Create undetected Chrome driver
        driver = uc.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        verbose_log("✅ Undetected Chrome browser started successfully")
        
        # Load existing cookies if available
        # First navigate to the domain to set cookies
        # Load cookies first, then navigate to the website
        # We need to visit the domain first to set cookies
        driver.get("https://app.teamskeet.com")
        load_cookies(driver)
        # Refresh the page to apply loaded cookies
        driver.refresh()
        
        # Check if user is already logged in
        already_logged_in = is_logged_in(driver)
        
        if already_logged_in:
            verbose_log("✅ User is already authenticated, skipping login process")
            navigate_and_save_cookies(driver, "https://app.teamskeet.com/")
        else:
            verbose_log("User needs to login, proceeding with login process...")
            verbose_log(f"Using email: {email}")
            verbose_log(f"Using password: {'*' * len(password)}")
            
            # Navigate to the login page
            navigate_and_save_cookies(driver, "https://app.teamskeet.com/")
            
            # Find and interact with email input
            verbose_log("Looking for email input field...")
            try:
                email_input = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='email'][name='email']"))
                )
                verbose_log("✅ Email input field found and visible")
            except TimeoutException:
                verbose_log("❌ Email input field not found - the login page may have changed or not loaded properly")
                verbose_log("Please check if the website is accessible and try again later")
                return
            
            verbose_log("Clicking email input field...")
            email_input.click()
            time.sleep(0.1)
            verbose_log("✅ Email input field clicked")
            
            verbose_log("Clearing email input field...")
            email_input.clear()
            time.sleep(0.1)
            verbose_log("✅ Email input field cleared")
            
            verbose_log(f"Typing email: {email}")
            email_input.send_keys(email)
            time.sleep(0.2)
            verbose_log("✅ Email typed successfully")
            
            # Find and interact with password input
            verbose_log("Looking for password input field...")
            try:
                password_input = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='password'][name='password']"))
                )
                verbose_log("✅ Password input field found and visible")
            except TimeoutException:
                verbose_log("❌ Password input field not found - the login page may have changed or not loaded properly")
                verbose_log("Please check if the website is accessible and try again later")
                return
            
            verbose_log("Clicking password input field...")
            password_input.click()
            time.sleep(0.1)
            verbose_log("✅ Password input field clicked")
            
            verbose_log("Clearing password input field...")
            password_input.clear()
            time.sleep(0.1)
            verbose_log("✅ Password input field cleared")
            
            verbose_log("Typing password...")
            password_input.send_keys(password)
            time.sleep(0.2)
            verbose_log("✅ Password typed successfully")
            
            # Find and click login button
            verbose_log("Looking for login button...")
            try:
                login_button = WebDriverWait(driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Login') or @type='submit']"))
                )
                verbose_log("✅ Login button found and visible")
            except TimeoutException:
                verbose_log("❌ Login button not found - the login page may have changed or not loaded properly")
                verbose_log("Please check if the website is accessible and try again later")
                return
            
            verbose_log("Clicking login button...")
            login_button.click()
            verbose_log("✅ Login button clicked successfully")
            
            # Wait for login to complete
            verbose_log("Waiting for login to complete...")
            time.sleep(3)
            
            current_url = driver.current_url
            verbose_log(f"Current URL after login: {current_url}")
            
            if "login" in current_url.lower() or "auth" in current_url.lower():
                verbose_log("❌ Still on login page - login may have failed")
            else:
                verbose_log("✅ Login appears successful - redirected away from login page")
                # Save cookies immediately after successful login
                save_cookies(driver)
        
        # After authentication (either via cookies or login), look for and click "Access Your Content" button
        verbose_log("Looking for 'Access Your Content' button...")
        try:
            access_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div.access-button"))
            )
            verbose_log("✅ 'Access Your Content' button found")
            access_button.click()
            verbose_log("✅ 'Access Your Content' button clicked successfully")
            time.sleep(2)  # Wait for page to load after clicking
            save_cookies(driver)  # Save cookies after accessing content
        except TimeoutException:
            verbose_log("⚠️ 'Access Your Content' button not found - may not be required or page layout changed")
        
        # Navigate to specified URLs and extract movie URLs
        target_urls = [
            "https://app.teamskeet.com/series/599?page=1&ezFilter=294&pageSize=40",
            "https://app.teamskeet.com/series/384?page=1&ezFilter=294&pageSize=40"
        ]
        
        all_collected_urls = []  # Collect all URLs from all target URLs
        
        for url_index, target_url in enumerate(target_urls, 1):
            verbose_log(f"\n=== PROCESSING URL {url_index}/{len(target_urls)} ===")
            verbose_log(f"Navigating to: {target_url}")
            driver.get(target_url)
            time.sleep(3)  # Wait for page to load
            
            page_number = 1
            all_movie_urls = []
            
            while True:
                verbose_log(f"Processing page {page_number}...")
                
                # Extract all movie URLs matching the pattern
                movie_links = driver.find_elements(By.XPATH, "//a[contains(@href, '/movies/')]")
                page_movie_urls = []
                
                for link in movie_links:
                    href = link.get_attribute('href')
                    if href and '/movies/' in href and '?r=' in href:
                        page_movie_urls.append(href)
                
                # Remove duplicates and add to total list
                unique_page_urls = list(set(page_movie_urls))
                all_movie_urls.extend(unique_page_urls)
                
                verbose_log(f"Found {len(unique_page_urls)} movie URLs on page {page_number}")
                for i, url in enumerate(unique_page_urls, 1):
                    verbose_log(f"  {i}. {url}")
                
                # Check if next page button exists and is enabled
                try:
                    # Look for the next page button that is NOT disabled
                    next_buttons = driver.find_elements(By.CSS_SELECTOR, "button.ant-pagination-item-link")
                    next_button = None
                    
                    for button in next_buttons:
                        # Check if this button has the right arrow icon and is not disabled
                        try:
                            right_icon = button.find_element(By.CSS_SELECTOR, "span.anticon-right")
                            if not button.get_attribute("disabled"):
                                next_button = button
                                break
                        except:
                            continue
                    
                    if next_button:
                        verbose_log("Next page button found, clicking...")
                        driver.execute_script("arguments[0].click();", next_button)
                        time.sleep(3)  # Wait for next page to load
                        page_number += 1
                    else:
                        verbose_log("No more pages available - next button is disabled")
                        break
                        
                except Exception as e:
                    verbose_log(f"Error checking for next page button: {str(e)}")
                    verbose_log("No more pages available")
                    break
            
            # Display final count for this URL
            unique_all_urls = list(set(all_movie_urls))
            all_collected_urls.extend(unique_all_urls)  # Add to global collection
            verbose_log(f"\n=== FINAL RESULTS for {target_url} ===")
            verbose_log(f"Total unique movie URLs found: {len(unique_all_urls)}")
            for j, url in enumerate(unique_all_urls, 1):
                verbose_log(f"  {j}. {url}")
            verbose_log("=" * 50)
            verbose_log(f"✅ Completed processing URL {url_index}/{len(target_urls)}")
            
            # Add a small delay before processing next URL
            if url_index < len(target_urls):
                verbose_log("Waiting 2 seconds before processing next URL...")
                time.sleep(2)
        
        # Save all collected URLs to urls.txt (overwrite existing content)
        final_unique_urls = list(set(all_collected_urls))
        try:
            with open('urls.txt', 'w') as f:
                for url in final_unique_urls:
                    f.write(url + '\n')
            verbose_log(f"\n✅ Saved {len(final_unique_urls)} unique URLs to urls.txt")
        except Exception as e:
            verbose_log(f"❌ Error saving URLs to file: {str(e)}")
        
        verbose_log("Authentication process completed. Browser will remain open.")
        verbose_log("Press Ctrl+C to close the browser and exit.")
        
        # Keep browser open and monitor for URL changes
        try:
            current_url = driver.current_url
            cookie_save_counter = 0
            while True:
                time.sleep(1)
                cookie_save_counter += 1
                
                # Check if URL has changed (navigation occurred)
                new_url = driver.current_url
                if new_url != current_url:
                    verbose_log(f"URL changed from {current_url} to {new_url}")
                    save_cookies(driver)
                    current_url = new_url
                
                # Also save cookies every 30 seconds as backup
                if cookie_save_counter >= 30:
                    save_cookies(driver)
                    cookie_save_counter = 0
        except KeyboardInterrupt:
            verbose_log("Received interrupt signal. Final cookie save and closing browser...")
            save_cookies(driver)
            
    except TimeoutException as e:
        verbose_log(f"❌ Timeout error: {str(e)}")
        verbose_log("Element not found within the specified timeout")
    except NoSuchElementException as e:
        verbose_log(f"❌ Element not found: {str(e)}")
    except ValueError as e:
        verbose_log(f"❌ Configuration error: {str(e)}")
    except Exception as e:
        verbose_log(f"❌ Unexpected error: {str(e)}")
    finally:
        if driver:
            verbose_log("Closing browser...")
            driver.quit()
            verbose_log("✅ Browser closed successfully")

if __name__ == "__main__":
    main()