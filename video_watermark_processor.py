#!/usr/bin/env python3
"""
Script pour traiter les vidéos en lot: détection de logo, création de watermark et application.
Traite les vidéos de /Volumes/4To/videos/ et les sauvegarde dans /Volumes/1To/xxxBU/videos/
"""

import argparse
import json
import os
import random
import subprocess
import sys
from pathlib import Path
import time


def get_video_duration(video_path):
    """Obtient la durée d'une vidéo en secondes"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json', 
            '-show_format', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)
        return float(data['format']['duration'])
    except Exception as e:
        print(f"Erreur lors de l'obtention de la durée de {video_path}: {e}")
        return None


def detect_logo(video_path, debug=False):
    """Utilise logo_detector.py pour détecter le logo dans une vidéo"""
    try:
        cmd = ['python3', 'logo_detector.py', '-s', '20', '-m', '1', video_path, '-t', '0.90']
        if debug:
            cmd.append('--debug')

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if debug:
            # En mode debug, la sortie JSON est mélangée avec les messages verbeux
            # On cherche la ligne JSON (qui commence par '{' et finit par '}')
            lines = result.stdout.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('{') and line.endswith('}'):
                    try:
                        return json.loads(line)
                    except json.JSONDecodeError:
                        continue
            return None
        else:
            # En mode normal, toute la sortie est JSON
            return json.loads(result.stdout.strip())
            
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors de la détection du logo: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Erreur lors du parsing JSON: {e}")
        return None


def create_watermark(width, height, debug=False):
    """Utilise logo_maker.py pour créer un watermark"""
    try:
        cmd = ['python3', 'logo_maker.py', str(height), str(width)]
        if debug:
            print(f"Création du watermark {width}x{height}...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if debug:
            print(result.stdout)
        
        # Le fichier créé sera tmp/{height}-{width}.png
        watermark_path = f"tmp/{height}-{width}.png"
        return watermark_path if os.path.exists(watermark_path) else None
        
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors de la création du watermark: {e}")
        return None


def process_video_chunk(input_path, output_path, start_time, duration, logo_region, watermark_path, debug=False):
    """Traite un chunk de vidéo avec le watermark"""
    try:
        if debug:
            print(f"Traitement de {input_path} -> {output_path}")
            print(f"Chunk: {start_time}s - {start_time + duration}s")
            print(f"Logo détecté: {logo_region}")
            print(f"Watermark: {watermark_path}")
        
        # Construire la commande ffmpeg
        cmd = [
            'ffmpeg', '-y',  # -y pour écraser le fichier de sortie
            '-ss', str(start_time),  # Position de début
            '-i', input_path,  # Vidéo d'entrée
            '-t', str(duration),  # Durée du chunk
            '-i', watermark_path,  # Watermark
            '-filter_complex',
            f'[0:v][1:v]overlay={logo_region["x"]}:{logo_region["y"]}[v]',  # Overlay du watermark
            '-map', '[v]',  # Utiliser la vidéo avec overlay
            '-map', '0:a?',  # Copier l'audio si présent
            '-c:v', 'h264_videotoolbox',  # Codec vidéo avec accélération matérielle M4
            '-c:a', 'copy',  # Copier l'audio sans re-encodage
            '-b:v', '5M',  # Bitrate vidéo pour VideoToolbox
            output_path
        ]
        
        if debug:
            print(f"Commande ffmpeg: {' '.join(cmd)}")
            print("Exécution de ffmpeg...")
            # En mode debug, afficher la sortie ffmpeg en temps réel
            result = subprocess.run(cmd, text=True, check=True)
            print("ffmpeg terminé avec succès")
        else:
            # En mode normal, capturer la sortie
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors du traitement vidéo: {e}")
        if debug:
            print("Erreur ffmpeg:")
            if hasattr(e, 'stderr') and e.stderr:
                print(e.stderr)
            if hasattr(e, 'stdout') and e.stdout:
                print("Stdout:")
                print(e.stdout)
        return False


def get_video_files(directory):
    """Récupère la liste des fichiers vidéo dans un répertoire"""
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
    video_files = []

    directory = Path(directory)
    if not directory.exists():
        print(f"Erreur: Le répertoire {directory} n'existe pas")
        return []

    for file_path in directory.iterdir():
        # Ignorer les fichiers cachés et les métadonnées macOS
        if (file_path.is_file() and
            file_path.suffix.lower() in video_extensions and
            not file_path.name.startswith('.') and
            not file_path.name.startswith('._')):
            video_files.append(file_path)

    return sorted(video_files)


def main():
    parser = argparse.ArgumentParser(
        description="Traite les vidéos en lot avec détection de logo et watermark"
    )
    parser.add_argument('--debug', '-bug', action='store_true',
                       help='Mode verbeux avec informations de debug')
    parser.add_argument('--test', '-try', action='store_true',
                       help='Mode test: traiter seulement une vidéo')
    parser.add_argument('--input-dir', default='/Volumes/4To/videos/',
                       help='Répertoire source des vidéos (défaut: /Volumes/4To/videos/)')
    parser.add_argument('--output-dir', default='/Volumes/1To/xxxBU/videos/',
                       help='Répertoire de sortie (défaut: /Volumes/1To/xxxBU/videos/)')
    parser.add_argument('--chunk-duration', type=int, default=480,
                       help='Durée du chunk en secondes (défaut: 480 = 8 minutes)')
    
    args = parser.parse_args()
    
    # Vérifier les répertoires
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    if not input_dir.exists():
        print(f"Erreur: Le répertoire source {input_dir} n'existe pas")
        sys.exit(1)
    
    # Créer le répertoire de sortie s'il n'existe pas
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Obtenir la liste des vidéos
    video_files = get_video_files(input_dir)
    if not video_files:
        print(f"Aucune vidéo trouvée dans {input_dir}")
        sys.exit(1)
    
    if args.debug:
        print(f"Trouvé {len(video_files)} vidéos dans {input_dir}")
    
    # En mode test, ne traiter qu'une seule vidéo
    if args.test:
        video_files = video_files[:1]
        if args.debug:
            print("Mode test: traitement d'une seule vidéo")
    
    # Déterminer le temps de début aléatoire pour le chunk (une seule fois pour toutes les vidéos)
    # On prendra un début aléatoire dans les premières 10 minutes de la première vidéo
    first_video = video_files[0]
    first_duration = get_video_duration(first_video)
    
    if first_duration is None:
        print(f"Impossible d'obtenir la durée de {first_video}")
        sys.exit(1)
    
    # Calculer le temps de début aléatoire (dans les 10 premières minutes ou la moitié de la vidéo)
    max_start_time = min(600, first_duration / 2)  # 10 minutes ou la moitié
    if max_start_time < args.chunk_duration:
        print(f"Vidéo trop courte pour extraire un chunk de {args.chunk_duration}s")
        sys.exit(1)
    
    # Temps de début aléatoire
    start_time = random.uniform(0, max_start_time - args.chunk_duration)
    
    if args.debug:
        print(f"Temps de début sélectionné: {start_time:.2f}s")
        print(f"Durée du chunk: {args.chunk_duration}s")
        print(f"Fin du chunk: {start_time + args.chunk_duration:.2f}s")
    
    # Traiter chaque vidéo
    processed_count = 0
    failed_count = 0
    
    for i, video_path in enumerate(video_files, 1):
        if args.debug:
            print(f"\n{'='*60}")
            print(f"Traitement de la vidéo {i}/{len(video_files)}: {video_path.name}")
            print(f"{'='*60}")
        
        try:
            # Vérifier la durée de la vidéo
            duration = get_video_duration(video_path)
            if duration is None or duration < start_time + args.chunk_duration:
                print(f"Vidéo {video_path.name} trop courte, ignorée")
                failed_count += 1
                continue
            
            # Détecter le logo
            if args.debug:
                print("Détection du logo...")
            
            logo_region = detect_logo(str(video_path), args.debug)
            if logo_region is None:
                print(f"Aucun logo détecté dans {video_path.name}, ignorée")
                failed_count += 1
                continue
            
            # Créer le watermark
            if args.debug:
                print("Création du watermark...")
            
            watermark_path = create_watermark(
                logo_region['width'], 
                logo_region['height'], 
                args.debug
            )
            
            if watermark_path is None:
                print(f"Échec de la création du watermark pour {video_path.name}")
                failed_count += 1
                continue
            
            # Définir le chemin de sortie
            output_path = output_dir / f"{video_path.name}"
            
            # Traiter la vidéo
            if args.debug:
                print("Traitement de la vidéo...")
            
            success = process_video_chunk(
                str(video_path),
                str(output_path),
                start_time,
                args.chunk_duration,
                logo_region,
                watermark_path,
                args.debug
            )
            
            if success:
                processed_count += 1
                if args.debug:
                    print(f"✓ Vidéo traitée avec succès: {output_path}")
                else:
                    print(f"✓ {video_path.name} -> {output_path.name}")
            else:
                failed_count += 1
                print(f"✗ Échec du traitement de {video_path.name}")
        
        except Exception as e:
            print(f"Erreur lors du traitement de {video_path.name}: {e}")
            failed_count += 1
    
    # Résumé final
    print(f"\n{'='*60}")
    print(f"RÉSUMÉ")
    print(f"{'='*60}")
    print(f"Vidéos traitées avec succès: {processed_count}")
    print(f"Vidéos échouées: {failed_count}")
    print(f"Total: {len(video_files)}")

    if processed_count > 0:
        print(f"Vidéos sauvegardées dans: {output_dir}")

    if args.debug:
        print(f"\nParamètres utilisés:")
        print(f"  - Répertoire source: {input_dir}")
        print(f"  - Répertoire sortie: {output_dir}")
        print(f"  - Durée chunk: {args.chunk_duration}s")
        print(f"  - Temps de début: {start_time:.2f}s")


if __name__ == "__main__":
    main()
