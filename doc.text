// on hover
const el = document.querySelector('#vjs_video_3_THEOplayerFi');
el.style.setProperty('background-color', 'yellow', 'important');
el.style.transition = 'background-color 1s';
el.addEventListener('mouseover', () => {
  el.style.backgroundColor = 'green';
});

el.addEventListener('mouseout', () => {
  el.style.backgroundColor = '';
});roundColor = '';
});


// click
document.querySelector('[id^="popupsmart-container"] > svg').click();

// check presence
!!document.querySelector("#block-cqr2mb0koh40 > div")













document.querySelector('#root > div > section > main > div > div > div > header > div.promobar-container > div > div.close-button').click();

ffmpeg -i "[stream_url]" -c copy ./videos/[id].mp4