#!/usr/bin/env python3
"""
Script pour détecter la position et les dimensions d'un logo fixe dans une vidéo.
Échantillonne des images en évitant les premières et dernières minutes.
"""

import cv2
import numpy as np
import argparse
import os
import subprocess
import sys
from pathlib import Path
import tempfile
import json

def get_video_info(video_path):
    """Obtient les informations de la vidéo (durée, fps, dimensions)"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Impossible d'ouvrir la vidéo: {video_path}")
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = frame_count / fps
    
    cap.release()
    
    return {
        'fps': fps,
        'frame_count': frame_count,
        'width': width,
        'height': height,
        'duration': duration
    }

def extract_sample_frames(video_path, num_samples=10, skip_minutes=1):
    """Extrait des frames échantillonnées de la vidéo"""
    info = get_video_info(video_path)
    duration = info['duration']
    fps = info['fps']
    
    # Calculer les limites temporelles
    skip_seconds = skip_minutes * 60
    start_time = skip_seconds
    end_time = duration - skip_seconds
    
    if end_time <= start_time:
        raise ValueError(f"Vidéo trop courte ({duration:.1f}s) pour ignorer {skip_minutes} minutes au début et à la fin")
    
    # Calculer les timestamps pour l'échantillonnage
    sample_duration = end_time - start_time
    time_interval = sample_duration / (num_samples + 1)
    sample_times = [start_time + i * time_interval for i in range(1, num_samples + 1)]
    
    print(f"Durée vidéo: {duration:.1f}s")
    print(f"Échantillonnage entre {start_time:.1f}s et {end_time:.1f}s")
    print(f"Extraction de {num_samples} frames...")
    
    cap = cv2.VideoCapture(video_path)
    frames = []
    
    for i, time_sec in enumerate(sample_times):
        frame_number = int(time_sec * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = cap.read()
        
        if ret:
            frames.append(frame)
            print(f"Frame {i+1}/{num_samples} extraite à {time_sec:.1f}s")
        else:
            print(f"Erreur lors de l'extraction de la frame à {time_sec:.1f}s")
    
    cap.release()
    return frames, info

def detect_logo_region(frames, threshold=0.8, min_logo_size=50):
    """Détecte la région du logo en analysant les zones statiques"""
    if not frames:
        raise ValueError("Aucune frame disponible")

    print("Analyse des zones statiques...")

    # Convertir toutes les frames en niveaux de gris
    gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames]

    # Méthode 1: Analyse de variance
    stacked_frames = np.stack(gray_frames, axis=0)
    variance_map = np.var(stacked_frames.astype(np.float32), axis=0)

    # Méthode 2: Différence absolue moyenne par rapport à la première frame
    reference_frame = gray_frames[0].astype(np.float32)
    diff_maps = []
    for frame in gray_frames[1:]:
        diff = np.abs(frame.astype(np.float32) - reference_frame)
        diff_maps.append(diff)

    if diff_maps:
        mean_diff_map = np.mean(diff_maps, axis=0)
    else:
        mean_diff_map = np.zeros_like(variance_map)

    # Combiner les deux méthodes (variance faible ET différence faible)
    variance_normalized = (variance_map - np.min(variance_map)) / (np.max(variance_map) - np.min(variance_map) + 1e-8)
    diff_normalized = (mean_diff_map - np.min(mean_diff_map)) / (np.max(mean_diff_map) - np.min(mean_diff_map) + 1e-8)

    # Score de stabilité (plus proche de 1 = plus stable)
    stability_score = (1 - variance_normalized) * (1 - diff_normalized)

    # Appliquer un seuil adaptatif
    stability_uint8 = (stability_score * 255).astype(np.uint8)

    # Utiliser un seuil plus intelligent
    if threshold > 0.95:
        # Pour des seuils très élevés, utiliser un seuil adaptatif
        thresh_value = cv2.threshold(stability_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[0]
        print(f"Seuil adaptatif calculé: {thresh_value/255:.3f}")
    else:
        thresh_value = int(255 * threshold)

    _, binary_map = cv2.threshold(stability_uint8, thresh_value, 255, cv2.THRESH_BINARY)

    # Opérations morphologiques pour nettoyer la détection
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    binary_map = cv2.morphologyEx(binary_map, cv2.MORPH_CLOSE, kernel)
    binary_map = cv2.morphologyEx(binary_map, cv2.MORPH_OPEN, kernel)

    # Trouver les contours des zones statiques
    contours, _ = cv2.findContours(binary_map, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        print("Aucune zone statique détectée")
        return None

    # Filtrer les contours trop petits (probablement du bruit)
    min_area = min_logo_size  # pixels
    valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]

    if not valid_contours:
        print("Aucune zone statique suffisamment grande détectée")
        return None

    # Si plusieurs contours, essayer de les regrouper s'ils sont proches
    if len(valid_contours) > 1:
        print(f"Détection de {len(valid_contours)} zones statiques, tentative de regroupement...")

        # Calculer les boîtes englobantes de tous les contours valides
        boxes = [cv2.boundingRect(c) for c in valid_contours]

        # Trouver la boîte englobante globale
        min_x = min(box[0] for box in boxes)
        min_y = min(box[1] for box in boxes)
        max_x = max(box[0] + box[2] for box in boxes)
        max_y = max(box[1] + box[3] for box in boxes)

        x, y, w, h = min_x, min_y, max_x - min_x, max_y - min_y

        # Vérifier si le regroupement est raisonnable (pas trop dispersé)
        total_contour_area = sum(cv2.contourArea(c) for c in valid_contours)
        bounding_area = w * h

        if total_contour_area / bounding_area > 0.1:  # Au moins 10% de la boîte est du logo
            print(f"Regroupement réussi: {len(valid_contours)} zones -> 1 région")
        else:
            # Prendre le plus grand contour
            largest_contour = max(valid_contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)
            print("Regroupement échoué, utilisation du plus grand contour")
    else:
        # Un seul contour valide
        x, y, w, h = cv2.boundingRect(valid_contours[0])

    # Ajouter une marge autour du logo détecté
    margin = 10
    x = max(0, x - margin)
    y = max(0, y - margin)
    w = min(frames[0].shape[1] - x, w + 2 * margin)
    h = min(frames[0].shape[0] - y, h + 2 * margin)

    return {
        'x': x,
        'y': y,
        'width': w,
        'height': h,
        'area': w * h
    }

def analyze_logo_variations(frames):
    """Analyse détaillée des variations dans les frames pour comprendre le problème"""
    print("\n=== ANALYSE DES VARIATIONS ===")

    gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames]
    stacked_frames = np.stack(gray_frames, axis=0)

    # Statistiques globales
    variance_map = np.var(stacked_frames.astype(np.float32), axis=0)
    mean_variance = np.mean(variance_map)
    max_variance = np.max(variance_map)
    min_variance = np.min(variance_map)

    print(f"Variance globale - Min: {min_variance:.2f}, Max: {max_variance:.2f}, Moyenne: {mean_variance:.2f}")

    # Analyse par percentiles
    percentiles = [90, 95, 99, 99.5, 99.9]
    for p in percentiles:
        threshold_val = np.percentile(variance_map, p)
        stable_pixels = np.sum(variance_map <= threshold_val)
        total_pixels = variance_map.size
        percentage = (stable_pixels / total_pixels) * 100
        print(f"Percentile {p}%: variance <= {threshold_val:.2f} -> {stable_pixels} pixels ({percentage:.1f}%)")

    # Analyse des différences frame par frame
    print("\nAnalyse des différences entre frames consécutives:")
    reference_frame = gray_frames[0].astype(np.float32)

    for i, frame in enumerate(gray_frames[1:], 1):
        diff = np.abs(frame.astype(np.float32) - reference_frame)
        mean_diff = np.mean(diff)
        max_diff = np.max(diff)
        stable_pixels = np.sum(diff <= 1.0)  # Pixels avec différence <= 1
        percentage = (stable_pixels / diff.size) * 100
        print(f"  Frame {i}: diff moyenne={mean_diff:.2f}, max={max_diff:.2f}, pixels stables={stable_pixels} ({percentage:.1f}%)")

    return variance_map

def debug_detection(frames, logo_region, threshold):
    """Affiche des images de debug pour analyser la détection"""
    print("\n=== MODE DEBUG ===")

    # Créer le dossier debug s'il n'existe pas
    debug_dir = Path("debug")
    debug_dir.mkdir(exist_ok=True)
    print(f"Sauvegarde des images de debug dans: {debug_dir.absolute()}")

    # Analyse préliminaire
    variance_map = analyze_logo_variations(frames)

    # Convertir les frames en niveaux de gris
    gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames]

    # Calculer les cartes de stabilité
    stacked_frames = np.stack(gray_frames, axis=0)

    reference_frame = gray_frames[0].astype(np.float32)
    diff_maps = []
    for frame in gray_frames[1:]:
        diff = np.abs(frame.astype(np.float32) - reference_frame)
        diff_maps.append(diff)

    if diff_maps:
        mean_diff_map = np.mean(diff_maps, axis=0)
    else:
        mean_diff_map = np.zeros_like(variance_map)

    # Normaliser
    variance_normalized = (variance_map - np.min(variance_map)) / (np.max(variance_map) - np.min(variance_map) + 1e-8)
    diff_normalized = (mean_diff_map - np.min(mean_diff_map)) / (np.max(mean_diff_map) - np.min(mean_diff_map) + 1e-8)
    stability_score = (1 - variance_normalized) * (1 - diff_normalized)

    # Créer plusieurs versions avec différents seuils pour comparaison
    debug_frame = frames[0].copy()
    if logo_region:
        x, y, w, h = logo_region['x'], logo_region['y'], logo_region['width'], logo_region['height']
        cv2.rectangle(debug_frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
        cv2.putText(debug_frame, f"Detected: {w}x{h}", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    # Créer des cartes avec différents seuils
    thresholds_to_test = [0.7, 0.8, 0.9, 0.95, 0.99]
    for i, test_thresh in enumerate(thresholds_to_test):
        stability_uint8 = (stability_score * 255).astype(np.uint8)
        _, binary_map = cv2.threshold(stability_uint8, int(255 * test_thresh), 255, cv2.THRESH_BINARY)

        # Appliquer les opérations morphologiques
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary_map = cv2.morphologyEx(binary_map, cv2.MORPH_CLOSE, kernel)
        binary_map = cv2.morphologyEx(binary_map, cv2.MORPH_OPEN, kernel)

        cv2.imwrite(str(debug_dir / f'threshold_{test_thresh}.jpg'), binary_map)

        # Compter les pixels détectés
        detected_pixels = np.sum(binary_map > 0)
        total_pixels = binary_map.size
        percentage = (detected_pixels / total_pixels) * 100
        print(f"Seuil {test_thresh}: {detected_pixels} pixels détectés ({percentage:.2f}%)")

    # Sauvegarder les images de debug principales
    cv2.imwrite(str(debug_dir / 'original.jpg'), frames[0])
    cv2.imwrite(str(debug_dir / 'variance.jpg'), (variance_normalized * 255).astype(np.uint8))
    cv2.imwrite(str(debug_dir / 'diff.jpg'), (diff_normalized * 255).astype(np.uint8))
    cv2.imwrite(str(debug_dir / 'stability.jpg'), (stability_score * 255).astype(np.uint8))
    cv2.imwrite(str(debug_dir / 'detection.jpg'), debug_frame)

    # Créer une image avec heatmap de stabilité
    stability_colored = cv2.applyColorMap((stability_score * 255).astype(np.uint8), cv2.COLORMAP_JET)
    cv2.imwrite(str(debug_dir / 'stability_heatmap.jpg'), stability_colored)

    print(f"\nImages de debug sauvegardées dans {debug_dir}/:")
    print("  - original.jpg: Frame de référence")
    print("  - variance.jpg: Carte de variance (noir = variable, blanc = stable)")
    print("  - diff.jpg: Carte de différence (noir = identique, blanc = différent)")
    print("  - stability.jpg: Score de stabilité combiné (blanc = très stable)")
    print("  - stability_heatmap.jpg: Heatmap de stabilité (rouge = stable, bleu = variable)")
    print("  - detection.jpg: Détection finale avec rectangle")
    print("  - threshold_X.jpg: Détections avec différents seuils")

    # Suggestions basées sur l'analyse
    print("\n=== SUGGESTIONS ===")
    stable_pixels_90 = np.sum(stability_score >= 0.9)
    stable_pixels_80 = np.sum(stability_score >= 0.8)
    stable_pixels_70 = np.sum(stability_score >= 0.7)

    print(f"Pixels très stables (≥0.9): {stable_pixels_90}")
    print(f"Pixels stables (≥0.8): {stable_pixels_80}")
    print(f"Pixels moyennement stables (≥0.7): {stable_pixels_70}")

    if stable_pixels_90 < 1000:
        print("⚠️  Très peu de pixels parfaitement stables détectés.")
        print("   Le logo pourrait avoir de légères variations (compression, bruit, micro-mouvements)")
        print("   Essayez un seuil plus bas (0.7-0.8) ou augmentez le nombre d'échantillons")

    if stable_pixels_80 > stable_pixels_90 * 5:
        print("💡 Beaucoup plus de pixels stables avec un seuil de 0.8")
        print("   Recommandation: utilisez --threshold 0.8")



def show_ffplay_command(video_path, logo_region):
    """Affiche les commandes ffplay testées et fonctionnelles"""
    if not logo_region:
        print("Aucune région de logo détectée")
        return None

    x, y, w, h = logo_region['x'], logo_region['y'], logo_region['width'], logo_region['height']

    print(f"\n=== COMMANDES FFPLAY (TESTÉES ET FONCTIONNELLES) ===")
    print(f"Région détectée: x={x}, y={y}, largeur={w}, hauteur={h}")

    # Commande 1: Afficher le cadre du logo détecté ✓ TESTÉE
    filter_frame = f"drawbox=x={x}:y={y}:w={w}:h={h}:color=red:thickness=3"
    cmd_frame = f"ffplay -i '{video_path}' -vf '{filter_frame}' -window_title 'Cadre du logo détecté'"

    print(f"\n1. ✓ Cadre du logo détecté:")
    print(f"   {cmd_frame}")

    # Commande 2: Masquer le logo avec un rectangle noir ✓ TESTÉE
    filter_hide_black = f"drawbox=x={x}:y={y}:w={w}:h={h}:color=black:thickness=fill"
    cmd_hide_black = f"ffplay -i '{video_path}' -vf '{filter_hide_black}' -window_title 'Logo masqué (noir)'"

    print(f"\n2. ✓ Logo masqué (rectangle noir):")
    print(f"   {cmd_hide_black}")

    # Commande 3: Suppression avec delogo ✓ TESTÉE
    filter_delogo = f"delogo=x={x}:y={y}:w={w}:h={h}"
    cmd_delogo = f"ffplay -i '{video_path}' -vf '{filter_delogo}' -window_title 'Logo supprimé (delogo)'"

    print(f"\n3. ✓ Logo supprimé avec delogo:")
    print(f"   {cmd_delogo}")

    print(f"\n(Appuyez sur 'q' dans ffplay pour quitter)")
    print(f"Note: Ces commandes ont été testées et fonctionnent avec votre installation ffplay")

    return {
        'cadre': cmd_frame,
        'masque': cmd_hide_black,
        'delogo': cmd_delogo
    }

def show_ffmpeg_commands(video_path, logo_region):
    """Affiche les commandes ffmpeg optimisées pour Mac M4 pour supprimer le logo de la vidéo"""
    if not logo_region:
        print("Aucune région de logo détectée")
        return None

    x, y, w, h = logo_region['x'], logo_region['y'], logo_region['width'], logo_region['height']
    video_name = Path(video_path).stem

    print(f"\n=== COMMANDES FFMPEG POUR SUPPRIMER LE LOGO (Optimisées Mac M4) ===")

    # Options communes pour Mac M4 avec VideoToolbox
    hw_options = "-c:v h264_videotoolbox -allow_sw 1 -realtime 0"

    # Commande 1: Masquer avec un rectangle noir
    cmd_black = f"ffmpeg -hwaccel videotoolbox -i '{video_path}' -vf 'drawbox=x={x}:y={y}:w={w}:h={h}:color=black:thickness=fill' {hw_options} '{video_name}_logo_masked.mp4'"
    print(f"\n1. Masquer le logo avec un rectangle noir:")
    print(f"   {cmd_black}")

    # Commande 2: Flouter le logo (version corrigée)
    filter_blur = f"split[main][logo];[logo]crop={w}:{h}:{x}:{y},gblur=sigma=10[blurred];[main][blurred]overlay={x}:{y}"
    cmd_blur = f"ffmpeg -hwaccel videotoolbox -i '{video_path}' -filter_complex '{filter_blur}' {hw_options} '{video_name}_logo_blurred.mp4'"
    print(f"\n2. Flouter le logo:")
    print(f"   {cmd_blur}")

    # Commande 3: Suppression intelligente avec delogo
    cmd_delogo = f"ffmpeg -hwaccel videotoolbox -i '{video_path}' -vf 'delogo=x={x}:y={y}:w={w}:h={h}' {hw_options} '{video_name}_logo_delogo.mp4'"
    print(f"\n3. Suppression intelligente du logo (delogo):")
    print(f"   {cmd_delogo}")

    # Commande 4: Version simple avec flou gaussien sur la zone
    filter_simple_blur = f"boxblur=enable='between(x,{x},{x+w})*between(y,{y},{y+h})':luma_radius=10:chroma_radius=10"
    cmd_simple_blur = f"ffmpeg -hwaccel videotoolbox -i '{video_path}' -vf '{filter_simple_blur}' {hw_options} '{video_name}_logo_simple_blur.mp4'"
    print(f"\n4. Flou simple sur la zone du logo:")
    print(f"   {cmd_simple_blur}")

    # Commande 5: Version CPU pour compatibilité maximale
    cmd_cpu_black = f"ffmpeg -i '{video_path}' -vf 'drawbox=x={x}:y={y}:w={w}:h={h}:color=black:thickness=fill' -c:v libx264 -preset fast '{video_name}_logo_masked_cpu.mp4'"
    print(f"\n5. Version CPU (si problèmes avec VideoToolbox):")
    print(f"   {cmd_cpu_black}")

    print(f"\nNote: Les fichiers de sortie seront créés avec les préfixes:")
    print(f"  - {video_name}_logo_masked.mp4 (accélération matérielle)")
    print(f"  - {video_name}_logo_blurred.mp4 (accélération matérielle)")
    print(f"  - {video_name}_logo_delogo.mp4 (accélération matérielle)")
    print(f"  - {video_name}_logo_simple_blur.mp4 (accélération matérielle)")
    print(f"  - {video_name}_logo_masked_cpu.mp4 (version CPU)")

    print(f"\nAccélération matérielle: VideoToolbox (optimisé pour Mac M4)")

    return {
        'black': cmd_black,
        'blur': cmd_blur,
        'delogo': cmd_delogo,
        'simple_blur': cmd_simple_blur,
        'cpu_fallback': cmd_cpu_black
    }

def preview_logo_detection(video_path, logo_region):
    """Lance ffplay pour prévisualiser le cadre du logo détecté"""
    if not logo_region:
        print("Aucune région de logo à prévisualiser")
        return

    x, y, w, h = logo_region['x'], logo_region['y'], logo_region['width'], logo_region['height']

    # Créer un filtre ffmpeg pour dessiner un rectangle
    filter_complex = f"drawbox=x={x}:y={y}:w={w}:h={h}:color=red:thickness=3"

    cmd = [
        'ffplay',
        '-i', video_path,
        '-vf', filter_complex,
        '-window_title', f'Logo détecté: {x},{y} {w}x{h}'
    ]

    print(f"\nLancement de la prévisualisation...")
    print(f"Région détectée: x={x}, y={y}, largeur={w}, hauteur={h}")
    print(f"Commande: {' '.join(cmd)}")
    print("Appuyez sur 'q' dans ffplay pour quitter la prévisualisation")

    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Erreur lors du lancement de ffplay: {e}")
        print("Assurez-vous que ffplay est installé et accessible dans le PATH")
    except FileNotFoundError:
        print("ffplay n'est pas trouvé. Installez FFmpeg pour utiliser cette fonctionnalité.")



def main():
    parser = argparse.ArgumentParser(description="Détecte la position et les dimensions d'un logo fixe dans une vidéo")
    parser.add_argument('video', help='Chemin vers le fichier vidéo')
    parser.add_argument('-s', '--samples', type=int, default=10, help='Nombre d\'images à échantillonner (défaut: 10)')
    parser.add_argument('-m', '--skip-minutes', type=float, default=1.0, help='Minutes à ignorer au début et à la fin (défaut: 1.0)')
    parser.add_argument('-t', '--threshold', type=float, default=0.8, help='Seuil de détection des zones statiques (0-1, défaut: 0.8). Utilisez 0.99+ pour une détection très stricte avec seuil adaptatif.')
    parser.add_argument('--no-preview', action='store_true', help='Ne pas lancer la prévisualisation avec ffplay')
    parser.add_argument('--output-json', help='Sauvegarder les résultats dans un fichier JSON')
    parser.add_argument('--debug', action='store_true', help='Afficher des images de debug pour analyser la détection')
    parser.add_argument('--min-size', type=int, default=50, help='Taille minimale du logo en pixels (défaut: 50)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.video):
        print(f"Erreur: Le fichier vidéo '{args.video}' n'existe pas")
        sys.exit(1)
    
    try:
        # Extraire les frames échantillonnées
        frames, video_info = extract_sample_frames(args.video, args.samples, args.skip_minutes)
        
        if not frames:
            print("Aucune frame extraite. Vérifiez le fichier vidéo.")
            sys.exit(1)
        
        # Détecter la région du logo
        logo_region = detect_logo_region(frames, args.threshold, args.min_size)

        # Mode debug pour visualiser la détection
        if args.debug:
            debug_detection(frames, logo_region, args.threshold)

        # Préparer les résultats
        result = {
            'video_path': args.video,
            'video_info': video_info,
            'logo_region': logo_region,
            'detection_params': {
                'samples': args.samples,
                'skip_minutes': args.skip_minutes,
                'threshold': args.threshold,
                'min_size': args.min_size
            }
        }

        if logo_region:
            print(f"\n✓ Logo détecté:")
            print(f"  Position: ({logo_region['x']}, {logo_region['y']})")
            print(f"  Dimensions: {logo_region['width']}x{logo_region['height']}")
            print(f"  Surface: {logo_region['area']} pixels")

            # Sauvegarder en JSON si demandé
            if args.output_json:
                with open(args.output_json, 'w') as f:
                    json.dump(result, f, indent=2)
                print(f"Résultats sauvegardés dans: {args.output_json}")

            # Afficher les commandes (mais ne pas les exécuter en mode debug)
            if args.debug:
                show_ffplay_command(args.video, logo_region)
                show_ffmpeg_commands(args.video, logo_region)
            elif not args.no_preview:
                # Mode normal: exécuter ffplay automatiquement
                preview_logo_detection(args.video, logo_region)
            else:
                # Pas de preview mais afficher les commandes
                show_ffplay_command(args.video, logo_region)
                show_ffmpeg_commands(args.video, logo_region)
        else:
            print("\n✗ Aucun logo détecté")
            print("Essayez de:")
            print("  - Réduire le seuil avec --threshold (ex: 0.6)")
            print("  - Augmenter le nombre d'échantillons avec --samples")
            print("  - Réduire la taille minimale avec --min-size")
            print("  - Utiliser --debug pour analyser les zones détectées")
            print("  - Vérifier que le logo est bien fixe dans la vidéo")

            if args.output_json:
                with open(args.output_json, 'w') as f:
                    json.dump(result, f, indent=2)
                print(f"Résultats (aucun logo) sauvegardés dans: {args.output_json}")
    
    except Exception as e:
        print(f"Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
