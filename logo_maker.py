#!/usr/bin/env python3
"""
Script pour créer des logos redimensionnés avec arrière-plan blanc arrondi.
Prend en entrée les dimensions cibles et utilise ./logo.png comme source.
"""

import argparse
import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw


def create_rounded_rectangle(width, height, radius):
    """Crée un masque pour un rectangle avec coins arrondis"""
    # Créer une image avec canal alpha
    mask = Image.new('L', (width, height), 0)
    draw = ImageDraw.Draw(mask)
    
    # Dessiner le rectangle arrondi
    draw.rounded_rectangle(
        [(0, 0), (width, height)],
        radius=radius,
        fill=255
    )
    
    return mask


def resize_logo_with_background(logo_path, target_width, target_height, output_path):
    """
    Redimensionne le logo et le place sur un arrière-plan blanc arrondi
    
    Args:
        logo_path: Chemin vers le logo source
        target_width: Largeur cible de l'arrière-plan
        target_height: Hauteur cible de l'arrière-plan
        output_path: Chemin de sortie
    """
    try:
        # Charger le logo source
        logo = Image.open(logo_path)
        
        # Convertir en RGBA si nécessaire pour gérer la transparence
        if logo.mode != 'RGBA':
            logo = logo.convert('RGBA')
        
        # Calculer la nouvelle taille du logo en maintenant le ratio
        # Le logo sera redimensionné pour correspondre à la hauteur cible
        logo_ratio = logo.width / logo.height
        new_logo_height = target_height
        new_logo_width = int(new_logo_height * logo_ratio)
        
        # Si le logo redimensionné est plus large que la largeur cible,
        # ajuster en fonction de la largeur
        if new_logo_width > target_width:
            new_logo_width = target_width
            new_logo_height = int(new_logo_width / logo_ratio)
        
        # Redimensionner le logo
        resized_logo = logo.resize((new_logo_width, new_logo_height), Image.Resampling.LANCZOS)
        
        # Créer l'arrière-plan blanc avec coins arrondis
        background = Image.new('RGBA', (target_width, target_height), (255, 255, 255, 0))
        
        # Calculer le rayon pour les coins arrondis (10% de la plus petite dimension)
        corner_radius = min(target_width, target_height) // 10
        
        # Créer le masque pour les coins arrondis
        mask = create_rounded_rectangle(target_width, target_height, corner_radius)
        
        # Appliquer l'arrière-plan blanc avec le masque
        white_bg = Image.new('RGBA', (target_width, target_height), (255, 255, 255, 255))
        background.paste(white_bg, (0, 0), mask)
        
        # Calculer la position pour centrer le logo horizontalement
        x_offset = (target_width - new_logo_width) // 2
        y_offset = (target_height - new_logo_height) // 2
        
        # Coller le logo redimensionné sur l'arrière-plan
        background.paste(resized_logo, (x_offset, y_offset), resized_logo)
        
        # Convertir en RGB pour sauvegarder en PNG sans canal alpha si désiré
        # ou garder RGBA pour préserver la transparence des coins
        final_image = background.convert('RGB')
        
        # Sauvegarder l'image finale
        final_image.save(output_path, 'PNG', optimize=True)
        
        return True
        
    except Exception as e:
        print(f"Erreur lors du traitement de l'image: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Crée un logo redimensionné avec arrière-plan blanc arrondi"
    )
    parser.add_argument('height', type=int, help='Hauteur cible en pixels')
    parser.add_argument('width', type=int, help='Largeur cible en pixels')
    parser.add_argument('--source', default='./logo.png', 
                       help='Chemin vers le logo source (défaut: ./logo.png)')
    parser.add_argument('--output-dir', default='./tmp',
                       help='Répertoire de sortie (défaut: ./tmp)')
    parser.add_argument('--force', action='store_true',
                       help='Forcer la création même si le fichier existe déjà')
    
    args = parser.parse_args()
    
    # Vérifier que le logo source existe
    if not os.path.exists(args.source):
        print(f"Erreur: Le fichier logo source '{args.source}' n'existe pas")
        sys.exit(1)
    
    # Créer le répertoire de sortie s'il n'existe pas
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Générer le nom du fichier de sortie
    output_filename = f"{args.height}-{args.width}.png"
    output_path = output_dir / output_filename
    
    # Vérifier si le fichier existe déjà
    if output_path.exists() and not args.force:
        print(f"Le fichier '{output_path}' existe déjà. Utilisation du fichier existant.")
        print(f"Utilisez --force pour forcer la recréation.")
        return
    
    # Valider les dimensions
    if args.height <= 0 or args.width <= 0:
        print("Erreur: Les dimensions doivent être des entiers positifs")
        sys.exit(1)
    
    # Créer le logo
    print(f"Création du logo {args.width}x{args.height}...")
    print(f"Source: {args.source}")
    print(f"Sortie: {output_path}")
    
    success = resize_logo_with_background(
        args.source, 
        args.width, 
        args.height, 
        output_path
    )
    
    if success:
        print(f"✓ Logo créé avec succès: {output_path}")
    else:
        print("✗ Échec de la création du logo")
        sys.exit(1)


if __name__ == "__main__":
    main()
