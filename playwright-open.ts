// playwright-open.ts
// Launches a visible Chromium browser using <PERSON><PERSON> and navigates to the target URL.
// Handles login form if "Connexion au compte" is found, then navigates to upload page.
// Run with <PERSON><PERSON> after installing <PERSON><PERSON> and its browser binaries.
// 
// TIMEOUT POLICY: All timeouts are limited to 3 seconds maximum for faster execution
// HUMAN-LIKE INTERACTIONS: Realistic delays and natural typing patterns to avoid detection
// FUTURE FUNCTIONS: Always use 3-second max timeouts and add detailed comments

import { chromium } from "playwright";
import path from "path";
import * as dotenv from "dotenv";

import { promises as fs } from "fs";
import yaml from "js-yaml";

// Load environment variables from .env file
dotenv.config();

// URL Configuration - Multi-step navigation for natural browsing behavior
const initialUrl = "https://fr.xvideos.com/"; // Start at French homepage
const dashboardUrl = "https://fr.xvideos.com/account#_account-dashboard"; // Account dashboard
const contentUrl = "https://fr.xvideos.com/account#_account-content"; // Account content section
const finalUploadUrl = "https://fr.xvideos.com/account/uploads/new"; // Final upload destination

// Timeout constants (3 seconds max policy)
const MAX_TIMEOUT = 3000; // 3 seconds maximum for all operations
const PAGE_LOAD_TIMEOUT = 3000; // Wait for page elements to load
const LONGER_TIMEOUT = 5000; // deprecated, use PAGE_LOAD_TIMEOUT
const FORM_INTERACTION_TIMEOUT = 3000; // Wait for form interactions

// Human-like delay constants (realistic timing)
const HUMAN_DELAY_MIN = 800; // Minimum delay between actions (0.8s)
const HUMAN_DELAY_MAX = 1500; // Maximum delay between actions (1.5s)
const TYPING_DELAY_MIN = 50; // Minimum delay between keystrokes (50ms)
const TYPING_DELAY_MAX = 150; // Maximum delay between keystrokes (150ms)
const READING_DELAY = 1200; // Time to "read" page content (1.2s)

// Helper function to generate random human-like delays
function getRandomDelay(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Helper function to simulate human-like typing
async function humanType(page: any, selector: string, text: string) {
  // Click on the field first (humans do this)
  await page.click(selector);
  await page.waitForTimeout(getRandomDelay(200, 400));
  
  // Clear any existing content
  await page.fill(selector, '');
  await page.waitForTimeout(getRandomDelay(100, 300));
  
  // Type character by character with realistic delays
  for (const char of text) {
    await page.type(selector, char, { delay: getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX) });
  }
  
  // Small pause after typing (humans often pause to review what they typed)
  await page.waitForTimeout(getRandomDelay(300, 600));
}

// Reusable login function to handle authentication when needed
async function performLogin(page: any) {
  console.log("Login form detected. Proceeding with human-like login...");
  
  // Human-like delay before starting to interact with the form
  await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
  
  try {
    // Scroll to login form area (humans often scroll to see the form better)
    const loginForm = await page.locator('input[name="signin-form[login]"]').first();
    if (await loginForm.isVisible({ timeout: FORM_INTERACTION_TIMEOUT })) {
      await loginForm.scrollIntoViewIfNeeded();
      await page.waitForTimeout(getRandomDelay(400, 800));
    }
    
    // Fill email field with human-like typing
    console.log("Typing email address...");
    await humanType(page, 'input[name="signin-form[login]"]', process.env.XV_USERNAME || "");
    
    // Human delay between email and password (users often pause here)
    await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
    
    // Fill password field with human-like typing
    console.log("Typing password...");
    await humanType(page, 'input[name="signin-form[password]"]', process.env.XV_PASSWORD || "");
    
    // Human delay before checking remember me
    await page.waitForTimeout(getRandomDelay(600, 1000));
    
    // Check the "remember me" checkbox with human-like interaction
    console.log("Checking remember me option...");
    const rememberCheckbox = page.locator('input[name="signin-form[rememberme]"]');
    await rememberCheckbox.scrollIntoViewIfNeeded();
    await page.waitForTimeout(getRandomDelay(300, 600));
    await rememberCheckbox.click({ timeout: FORM_INTERACTION_TIMEOUT });
    console.log("Remember me checkbox checked");
    
    // Human delay before submitting (users often review their input)
    console.log("Reviewing form before submission...");
    await page.waitForTimeout(getRandomDelay(1000, 2000));
    
    // Find and click the submit button with human-like interaction
    const submitButton = await page.locator('input[type="submit"], button[type="submit"]').first();
    if (await submitButton.isVisible({ timeout: FORM_INTERACTION_TIMEOUT })) {
      await submitButton.scrollIntoViewIfNeeded();
      await page.waitForTimeout(getRandomDelay(400, 700));
      
      console.log("Submitting login form...");
      await submitButton.click({ timeout: FORM_INTERACTION_TIMEOUT });
      
      // Wait for navigation after login with human-like patience
      console.log("Waiting for login to complete...");
      await page.waitForLoadState("domcontentloaded", { timeout: PAGE_LOAD_TIMEOUT });
      await page.waitForTimeout(getRandomDelay(1500, 2500));
      
      // Verify login success and save cookies
      const currentUrl = page.url();
      if (!currentUrl.includes('signin') && !currentUrl.includes('login')) {
        console.log("Login successful - session cookies saved automatically");
        return true; // Login successful
      } else {
        console.log("Login may have failed - still on login page");
        return false; // Login failed
      }
    }
  } catch (error) {
    console.log("Error during login process:", error);
    return false; // Login failed
  }
  
  return false;
}

async function main() {
  // Create persistent context to maintain cookies across sessions
  // This ensures login state is preserved between script runs
  const userDataDir = path.join(__dirname, "browser-data");
  
  // Ensure the browser data directory exists
  if (!require('fs').existsSync(userDataDir)) {
    require('fs').mkdirSync(userDataDir, { recursive: true });
    console.log("Created browser data directory for cookie persistence");
  }
  // Remove stale Chromium singleton lock if present to avoid macOS crash/abort on next launch
  try {
    const lockFile = path.join(userDataDir, 'SingletonLock');
    if (require('fs').existsSync(lockFile)) {
      require('fs').unlinkSync(lockFile);
      console.log("Removed stale Chromium SingletonLock file.");
    }
  } catch (e) {
    console.log("Warning: Could not remove Chromium SingletonLock:", e);
  }
  
  const browser = await chromium.launchPersistentContext(userDataDir, { 
    headless: false, // Keep browser visible for debugging
    viewport: { width: 1280, height: 720 }, // Standard desktop viewport
    // Add human-like browser settings
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    // Ensure cookies and session data are preserved
    acceptDownloads: true,
    ignoreHTTPSErrors: true,
    // Command-line flags to disable crash dialogs and popups
     args: [
       '--disable-crash-reporter',           // Disable Chromium crash reporting
       '--disable-breakpad',                 // Disable crash dump collection
       '--disable-crash-dialog',             // Disable crash dialog popup
       '--disable-session-crashed-bubble',   // Disable "restore previous session" popup
       '--disable-restore-session-state',    // Disable session restore functionality
       '--no-first-run',                     // Skip first run experience
       '--no-default-browser-check',         // Skip default browser check
       '--disable-popup-blocking',           // Allow popups if needed
       '--disable-translate',                // Disable translation prompts
       '--disable-features=TranslateUI',     // Disable translate UI
       '--noerrdialogs'                      // Suppress error dialogs (best-effort)
     ]
  });

  // Auto-close the browser context when the user closes all windows to avoid macOS crash reporter
  browser.on('page', (p) => {
    p.on('close', async () => {
      try {
        // small delay to allow pages() list to update
        await new Promise((r) => setTimeout(r, 100));
        if (browser.pages().length === 0) {
          console.log('All browser windows closed by user. Closing context gracefully.');
          await browser.close();
        }
      } catch (err) {
        console.log('Error while auto-closing after last window closed:', err);
      }
    });
  });

  // Also handle unexpected process disconnects cleanly (using 'close' event instead)
  browser.on('close', () => {
    console.log('Browser context closed. Exiting cleanly.');
    process.exit(0);
  });
  
  // Graceful shutdown handling to prevent crash popups
  // Handle Ctrl+C (SIGINT) and other termination signals
  const gracefulShutdown = async (signal: string) => {
    console.log(`\nReceived ${signal}. Gracefully closing browser...`);
    try {
      await browser.close();
      console.log("Browser closed successfully.");
    } catch (error) {
      console.log("Error closing browser:", error);
    }
    process.exit(0);
  };

  // Register signal handlers for graceful shutdown
  process.on('SIGINT', () => gracefulShutdown('SIGINT')); // Ctrl+C
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // Termination signal
  process.on('SIGQUIT', () => gracefulShutdown('SIGQUIT')); // Quit signal
  
  // Get the first page or create a new one if none exists
  const page = browser.pages()[0] || await browser.newPage();
  
  // Check existing cookies to see if user might already be logged in
  const existingCookies = await browser.cookies();
  console.log(`Found ${existingCookies.length} existing cookies from previous sessions`);
  
  console.log(`Starting at French homepage: ${initialUrl}`);
  await page.goto(initialUrl, { waitUntil: "domcontentloaded" });
  
  // Simulate human reading time - users don't immediately interact with pages
  console.log("Reading homepage content...");
  await page.waitForTimeout(READING_DELAY);
  
  // Check if login form is present by looking for "Connexion au compte" text
  // This text indicates the user is not logged in and needs to authenticate
  const loginText = await page.textContent('body');
  if (loginText && loginText.includes("Connexion au compte")) {
    await performLogin(page);
  } else {
    console.log("No login form detected on homepage, user appears to be already logged in");
    // User is likely already logged in or page structure is different
    await page.waitForTimeout(getRandomDelay(800, 1200));
  }
  
  // Multi-step navigation for natural browsing behavior
  console.log("Beginning multi-step navigation to upload page...");
  
  // Step 1: Navigate to account dashboard
  console.log(`Step 1: Navigating to account dashboard: ${dashboardUrl}`);
  await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
  await page.goto(dashboardUrl, { waitUntil: "domcontentloaded", timeout: PAGE_LOAD_TIMEOUT });
  
  // Check if we're redirected to login page when accessing dashboard
  console.log("Checking if dashboard requires authentication...");
  await page.waitForTimeout(READING_DELAY);
  
  const dashboardContent = await page.textContent('body');
  if (dashboardContent && dashboardContent.includes("Connexion au compte")) {
    console.log("Dashboard redirected to login page. Authentication required.");
    const loginSuccess = await performLogin(page);
    
    if (loginSuccess) {
      console.log("Login successful. Returning to dashboard...");
      await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
      await page.goto(dashboardUrl, { waitUntil: "domcontentloaded", timeout: PAGE_LOAD_TIMEOUT });
      await page.waitForTimeout(READING_DELAY);
    } else {
      console.log("Login failed. Continuing with current page state...");
    }
  } else {
    console.log("Dashboard accessible. User is authenticated.");
  }
  
  // Human reading time for dashboard
  console.log("Reading account dashboard...");
  await page.waitForTimeout(READING_DELAY);
  
  // Step 2: Navigate to account content section
  console.log(`Step 2: Navigating to account content: ${contentUrl}`);
  await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
  await page.goto(contentUrl, { waitUntil: "domcontentloaded", timeout: PAGE_LOAD_TIMEOUT });
  
  // Human reading time for content section
  console.log("Reading account content section...");
  await page.waitForTimeout(READING_DELAY);
  
  // Step 3: Finally navigate to upload page
  console.log(`Step 3: Navigating to upload page: ${finalUploadUrl}`);
  await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
  await page.goto(finalUploadUrl, { waitUntil: "domcontentloaded", timeout: PAGE_LOAD_TIMEOUT });
  
  // Human reading time for the upload page
  console.log("Upload page loaded. Reading page content...");
  await page.waitForTimeout(READING_DELAY);

  // On some accounts, a fallback link is shown to continue to the basic upload form.
  // Example HTML:
  // <a href="/account/uploads/new?ssa=1">CONTINUER VERS LE FORMULAIRE D'UPLOAD</a>
  try {
    const continueHrefLocator = page.locator('a[href*="/account/uploads/new?ssa=1"]');
    const linkCount = await continueHrefLocator.count();

    if (linkCount > 0) {
      console.log("Found 'CONTINUER VERS LE FORMULAIRE D'UPLOAD' link by href. Clicking it...");
      await continueHrefLocator.first().scrollIntoViewIfNeeded();
      await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
      await continueHrefLocator.first().click({ timeout: FORM_INTERACTION_TIMEOUT });
      await page.waitForLoadState("domcontentloaded", { timeout: PAGE_LOAD_TIMEOUT });
      await page.waitForTimeout(READING_DELAY);
      console.log("Arrived on basic upload form via fallback link.");
    } else {
      // Fallback: try to find by visible link text (in case URL or params change)
      const continueTextLocator = page.locator('a', { hasText: "CONTINUER VERS LE FORMULAIRE D'UPLOAD" });
      if (await continueTextLocator.count() > 0) {
        console.log("Found 'CONTINUER VERS LE FORMULAIRE D'UPLOAD' link by text. Clicking it...");
        await continueTextLocator.first().scrollIntoViewIfNeeded();
        await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
        await continueTextLocator.first().click({ timeout: FORM_INTERACTION_TIMEOUT });
        await page.waitForLoadState("domcontentloaded", { timeout: PAGE_LOAD_TIMEOUT });
        await page.waitForTimeout(READING_DELAY);
        console.log("Arrived on basic upload form via fallback link (text match).");
      } else {
        console.log("No fallback upload form link detected on the page.");
      }
    }
  } catch (e) {
    console.log("Error while checking/activating fallback upload link:", e);
  }
  
  // Attempt to locate the basic upload form and submit the first file from ./videos
  try {
    const formLocator = page.locator('form#file_form');
    if ((await formLocator.count()) === 0) {
      console.log("[Upload] Upload form not found on the page. Skipping file selection.");
    } else {
      // List files in ./videos and pick the first regular file
      const videosDir = path.join(__dirname, "videos");
      let entries: string[] = [];
      try {
        entries = await fs.readdir(videosDir);
      } catch (e) {
        console.log(`[Upload] Could not read videos directory at ${videosDir}:`, e);
      }
      console.log(`[Upload] Files in ${videosDir}: ${entries.length ? entries.join(", ") : "(none)"}`);

      let selectedFilePath: string | null = null;
      // Prefer only .mp4 files (case-insensitive)
      const mp4Entries = entries.filter((n) => /\.mp4$/i.test(n));
      for (const name of mp4Entries) {
         try {
           const fullPath = path.join(videosDir, name);
           const st = await fs.stat(fullPath);
           if (st.isFile()) {
             selectedFilePath = fullPath;
             break;
           }
         } catch {}
       }

       if (!selectedFilePath) {
        console.log("[Upload] No .mp4 file found in ./videos. Nothing to upload.");
       } else {
         console.log(`[Upload] Selecting first file for upload: ${selectedFilePath}`);
         // Accept terms checkbox first
         const termsCheckbox = page.locator('#file_form_file_terms');
         if (await termsCheckbox.count()) {
          try {
            await termsCheckbox.scrollIntoViewIfNeeded();
            await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
            await termsCheckbox.check({ timeout: FORM_INTERACTION_TIMEOUT });
            console.log("[Upload] Terms checkbox checked.");
          } catch (e) {
            console.log("[Upload] Failed to check terms checkbox:", e);
          }
        } else {
          console.log("[Upload] Terms checkbox not found.");
        }

        // Set the file input
        const fileInput = page.locator('#file_form_file_file_options_file_1_file');
        try {
          await fileInput.scrollIntoViewIfNeeded();
          await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
          await fileInput.setInputFiles(selectedFilePath, { timeout: FORM_INTERACTION_TIMEOUT });
          console.log("[Upload] File selected in input.");
        } catch (e) {
          console.log("[Upload] Failed to set file in input:", e);
        }

        // Click the submit button to start the upload
        const submitBtn = formLocator.locator('button[type="submit"]');
        try {
          await submitBtn.scrollIntoViewIfNeeded();
          await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
          const enabled = await submitBtn.isEnabled();
          if (enabled) {
            await submitBtn.click({ timeout: FORM_INTERACTION_TIMEOUT });
            console.log(`[Upload] Submitted upload form for: ${path.basename(selectedFilePath)}`);
          } else {
            console.log("[Upload] Submit button is disabled. Upload not started.");
          }
        } catch (e) {
          console.log("[Upload] Failed to click submit button:", e);
        }

        // After submit: wait for upload page to show chunk progress, then pause, then fill metadata form
        try {
          // Wait for progress container visible
          const progressContainer = page.locator('#chunk-progress-container');
          await progressContainer.waitFor({ state: 'visible', timeout: LONGER_TIMEOUT });
          console.log('[Meta] Progress container visible. Attempting to pause upload.');

          // Hover progress container to reveal controls if they are hidden
          try {
            await progressContainer.scrollIntoViewIfNeeded();
            await progressContainer.hover({ timeout: FORM_INTERACTION_TIMEOUT });
          } catch {}

          // Click pause control (try several strategies)
          let paused = false;
          const pauseClickable = page.locator('button:has(span.icon-f.icf-player-pause-circle-o), a:has(span.icon-f.icf-player-pause-circle-o), [role="button"]:has(span.icon-f.icf-player-pause-circle-o)');
          try {
            if (await pauseClickable.count()) {
              await pauseClickable.first().scrollIntoViewIfNeeded();
              await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
              await pauseClickable.first().click({ timeout: FORM_INTERACTION_TIMEOUT });
              paused = true;
            }
          } catch {}

          if (!paused) {
            const pauseIcon = page.locator('span.icon-f.icf-player-pause-circle-o');
            try {
              if (await pauseIcon.count()) {
                await pauseIcon.first().scrollIntoViewIfNeeded();
                await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
                // Force click as a fallback if the element is not considered visible/clickable
                await pauseIcon.first().click({ timeout: FORM_INTERACTION_TIMEOUT, force: true });
                paused = true;
              }
            } catch {}
          }

          console.log(paused ? '[Meta] Clicked pause icon.' : '[Meta] Pause icon not found/clickable, continuing.');
          // Load YAML metadata based on selected file path
          const baseNoExt = selectedFilePath.replace(/\.[^/.]+$/, '');
          const yamlPath = baseNoExt + '.yaml';
          let meta: any = {};
          try {
            const yamlContent = await fs.readFile(yamlPath, 'utf8');
            meta = yaml.load(yamlContent) || {};
            console.log(`[Meta] Loaded YAML: ${yamlPath}`);
          } catch (e) {
            console.log('[Meta] YAML not found or failed to parse, continuing with empty metadata:', yamlPath);
          }

          const titleVal = typeof meta?.title === 'string' ? meta.title : '';
          const descVal = typeof meta?.desc === 'string' ? meta.desc : '';

          // Select category: straight (Hétéro)
          const heteroCheckbox = page.locator('#upload_form_category_category_centered_category_straight');
          if (await heteroCheckbox.count()) {
            try {
              await heteroCheckbox.scrollIntoViewIfNeeded();
              await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
              await heteroCheckbox.check({ timeout: FORM_INTERACTION_TIMEOUT });
              console.log('[Meta] Selected category: Hétéro.');
            } catch (e) {
              console.log('[Meta] Failed to select category Hétéro:', e);
            }
          } else {
            console.log('[Meta] Category checkbox Hétéro not found.');
          }

          // Fill title
          const titleInput = page.locator('#upload_form_titledesc_title');
          try {
            await titleInput.scrollIntoViewIfNeeded();
            await page.waitForTimeout(getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX));
            await titleInput.fill('');
            if (titleVal) {
              await titleInput.type(titleVal, { delay: getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX) });
            }
            console.log('[Meta] Title filled.');
          } catch (e) {
            console.log('[Meta] Failed to fill title:', e);
          }

          // Fill description
          const descTextarea = page.locator('#upload_form_titledesc_description');
          try {
            await descTextarea.scrollIntoViewIfNeeded();
            await page.waitForTimeout(getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX));
            await descTextarea.fill('');
            if (descVal) {
              await descTextarea.type(descVal, { delay: getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX) });
            }
            console.log('[Meta] Description filled.');
          } catch (e) {
            console.log('[Meta] Failed to fill description:', e);
          }
          
          // Add tags from YAML metadata
          try {
            const tags: string[] = Array.isArray(meta?.tags)
              ? (meta.tags as any[])
                  .filter((t) => typeof t === 'string' && t.trim())
                  .map((t) => (t as string).trim())
              : (typeof meta?.tags === 'string'
                  ? (meta.tags as string)
                      .split(/[;,]/)
                      .map((s) => s.trim())
                      .filter(Boolean)
                  : []);

            if (tags.length) {
              console.log(`[Meta] Adding ${tags.length} tag(s) from YAML...`);
              const tagGroup = page.getByRole('group', { name: /Ajouter des tags/i });
              if (!(await tagGroup.count())) {
                console.log('[Meta] Tag group not found ("Ajouter des tags"), skipping tag entry.');
              } else {
                const addButton = tagGroup.locator('button[type="button"][data-role="add"].add');
 
                for (const tag of tags) {
                  try {
                    if (!(await addButton.count())) {
                      console.log('[Meta] Tag add button not found, skipping remaining tags.');
                      break;
                    }
                    
                    await tagGroup.scrollIntoViewIfNeeded();
                    await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
                    
                    await addButton.first().click({ timeout: FORM_INTERACTION_TIMEOUT });
                    
                    // Try to find the focused input first, fallback to the last one
                    let tagInput = tagGroup.locator('input[id^="tag-adder-inp-"].focus').first();
                    try {
                      await tagInput.waitFor({ state: 'visible', timeout: FORM_INTERACTION_TIMEOUT });
                    } catch {
                      // Fallback to the last input if focus class isn't present
                      tagInput = tagGroup.locator('input[id^="tag-adder-inp-"]').last();
                      await tagInput.waitFor({ state: 'visible', timeout: FORM_INTERACTION_TIMEOUT });
                    }
                    await page.waitForTimeout(getRandomDelay(200, 400));
                    
                    // Human-like typing for the tag value within scoped input
                    await tagInput.click();
                    await tagInput.fill('');
                    await tagInput.type(tag, { delay: getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX) });
                    
                    // Press Enter to save the tag
                    await tagInput.press('Enter');
                    await page.waitForTimeout(getRandomDelay(400, 800));
                    console.log(`[Meta] Added tag: ${tag}`);
                  } catch (err) {
                    console.log(`[Meta] Failed to add tag \"${tag}\":`, err);
                  }
                }
              }
            } else {
              console.log('[Meta] No tags found in YAML, skipping tag entry.');
            }
          } catch (e) {
            console.log('[Meta] Error adding tags:', e);
          }

          // Add people from YAML metadata
          try {
            const people: string[] = Array.isArray(meta?.people)
              ? (meta.people as any[])
                  .filter((p) => typeof p === 'string' && p.trim())
                  .map((p) => (p as string).trim())
              : (typeof meta?.people === 'string'
                  ? (meta.people as string)
                      .split(/[;,]/)
                      .map((s) => s.trim())
                      .filter(Boolean)
                  : []);

            if (people.length) {
              console.log(`[Meta] Adding ${people.length} people from YAML...`);
              const modelsContainer = page.locator('div.models-list');
              if (!(await modelsContainer.count())) {
                console.log('[Meta] Models container not found, skipping people entry.');
              } else {
                const addButton = modelsContainer.locator('button.add');
                const personInput = modelsContainer.locator('input[type="text"]');
 
                for (const person of people) {
                  try {
                    if (!(await addButton.count())) {
                      console.log('[Meta] People add button not found, skipping remaining people.');
                      break;
                    }
                    
                    await modelsContainer.scrollIntoViewIfNeeded();
                    await page.waitForTimeout(getRandomDelay(HUMAN_DELAY_MIN, HUMAN_DELAY_MAX));
                    
                    // Click the add button first to activate the input
                    await addButton.first().click({ timeout: 1000 });
                    await page.waitForTimeout(getRandomDelay(200, 400));
                    
                    // Now find and use the input field
                    await personInput.first().click({ timeout: 1000 });
                    await personInput.first().fill('');
                    await personInput.first().type(person, { delay: getRandomDelay(TYPING_DELAY_MIN, TYPING_DELAY_MAX) });
                    
                    // Wait for the "Assigner" button to appear (with delay for DB lookup)
                    await page.waitForTimeout(getRandomDelay(800, 1500));
                    
                    // Click the "Assigner" button
                    const assignButton = page.locator('p.account-actions button.btn.btn-primary.assign.add-model');
                    try {
                      await assignButton.waitFor({ state: 'visible', timeout: 2000 });
                      await assignButton.click({ timeout: 1000 });
                      await page.waitForTimeout(getRandomDelay(400, 800));
                      
                      // Click "Fermer" button
                      const fermerButton = page.locator('div.pull-right button.cancel-model-info-form.btn.btn-default');
                      try {
                        await fermerButton.waitFor({ state: 'visible', timeout: 2000 });
                        await fermerButton.click({ timeout: 1000 });
                        await page.waitForTimeout(getRandomDelay(200, 400));
                        
                        // Click "OK" button
                        const okButton = page.locator('div.contextual-popup-actions button.ok-close.btn.btn-danger[data-send-form="1"]');
                        try {
                          await okButton.waitFor({ state: 'visible', timeout: 2000 });
                          await okButton.click({ timeout: 1000 });
                          await page.waitForTimeout(getRandomDelay(400, 800));
                          console.log(`[Meta] Assigned person: ${person}`);
                        } catch (okErr) {
                          console.log(`[Meta] "OK" button not found for person "${person}".`);
                        }
                      } catch (fermerErr) {
                        console.log(`[Meta] "Fermer" button not found for person "${person}".`);
                      }
                    } catch (assignErr) {
                      console.log(`[Meta] "Assigner" button not found for person "${person}", person may not be in database.`);
                    }
                  } catch (err) {
                    console.log(`[Meta] Failed to add person \"${person}\":`, err);
                  }
                }
              }
            } else {
              console.log('[Meta] No people found in YAML, skipping people entry.');
            }
          } catch (e) {
            console.log('[Meta] Error adding people:', e);
          }

        } catch (e) {
          console.log('[Meta] Error while pausing upload or filling metadata form:', e);
        }
      }
    }
  } catch (e) {
    console.log("[Upload] Unexpected error while trying to submit upload form:", e);
  }
  
  console.log("Multi-step navigation complete. Browser will remain open with persistent cookies.");
  console.log("All interactions completed with human-like timing patterns.");
  console.log("Press Ctrl+C to gracefully close the browser and exit.");
  
  // Keep the script alive until the browser is closed by the user or Ctrl+C is pressed
  // This prevents the script from terminating and closing the browser
  let exiting = false;
  const safeExit = () => {
    if (exiting) return;
    exiting = true;
    process.nextTick(() => process.exit(0));
  };
  browser.on("close", () => {
    console.log("Browser closed by user");
    safeExit();
  });
  
  // Keep process alive while there are open pages, exit when last page closes
  const maybeExitOnNoPages = async () => {
    await new Promise((r) => setTimeout(r, 100));
    if (browser.pages().length === 0) {
      console.log("No more open pages. Exiting.");
      safeExit();
    }
  };
  for (const p of browser.pages()) {
    p.on('close', maybeExitOnNoPages);
  }
  browser.on('page', (p) => p.on('close', maybeExitOnNoPages));

  // Instead of an infinite promise, resolve when the browser context closes
  await new Promise<void>((resolve) => {
    browser.once('close', () => {
      console.log('Browser context closed by user.');
      resolve();
    });
  });
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
  });

